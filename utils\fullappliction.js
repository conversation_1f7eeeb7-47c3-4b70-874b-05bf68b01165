// 首页映射关系
let mapping = {
  产业链: '/companyPackage/pages/industryChain/chainIndex/chainIndex',
  科技型企业: '/subPackage/pages/science/scienceEnter/index',
  融资事件: '/subPackage/pages/financing/home/<USER>',
  找关系: '/companyPackage/pages/mineRelation/relation',
  地图招商: '/companyPackage/pages/merchantsMap/merchants',
  企业猎搜: '/companyPackage/pages/searTerm/sear-term',
  风险监控: '/pages/companyInfo/companyInfo',
  权威榜单: '/companyPackage/pages/authoritativeList/authoritativeList',
  查园区: '/subPackage/pages/searchPark/index',
  全部应用: '/childSubpackage/pages/fullApplication/full'
};
let codeMap = {
  产业链: 'industrialChain',
  科技型企业: 'techEnterprise',
  融资事件: 'financingEvent',
  找关系: 'findRelationships',
  地图招商: 'mapInvestment',
  企业猎搜: 'enterpriseSearch',
  风险监控: 'riskMonitoring',
  权威榜单: 'authorityList',
  查园区: 'parkInquiry'
};
let allList = [
  {
    type: 'comprehensiveInquiry',
    title: '综合查询',
    code: 'comprehensiveInquiry',
    children: [
      {
        title: '企业猎搜',
        code: 'enterpriseSearch'
      },
      {
        title: '产业链',
        code: 'industrialChain'
      },
      {
        title: '地图招商',
        code: 'mapInvestment'
      },
      {
        title: '权威榜单',
        code: 'authorityList'
      },
      {
        title: '风险监控',
        code: 'riskMonitoring'
      },
      {
        title: '融资事件',
        code: 'financingEvent'
      },
      {
        title: '找关系',
        code: 'findRelationships'
      },
      {
        title: '查园区',
        code: 'parkInquiry'
      },
      {
        title: '科技型企业',
        code: 'techEnterprise'
      },
      {
        title: '查图谱',
        code: 'graphInquiry'
      },
      {
        title: '区域经济大全',
        code: 'regionalEconomy'
      },
      //  {
      //   "title": "查老板",
      //   "code": "bossInquiry",
      // },
      {
        title: '产业研报',
        code: 'industryReport'
      },
      {
        title: '外商招商',
        code: 'foreignInvestment'
      }
    ]
  },
  {
    type: 'comprehensiveInquiry',
    title: '负面信息',
    code: 'negativeInformation',
    children: [
      {
        title: '失信被执行人',
        code: 'dishonestExecutedPerson'
      },
      {
        title: '被执行人',
        code: 'executedPerson'
      },
      {
        title: '裁判文书',
        code: 'judgmentDocument'
      },
      {
        title: '立案信息',
        code: 'filingInformation'
      },
      {
        title: '终本案件',
        code: 'finalCase'
      },
      {
        title: '开庭公告',
        code: 'courtAnnouncement'
      },
      {
        title: '法院公告',
        code: 'courtNotice'
      },
      {
        title: '限制高消费',
        code: 'highConsumptionRestriction'
      },
      {
        title: '行政处罚',
        code: 'administrativePenalty'
      },
      {
        title: '进出口信用',
        code: 'importExportCredit'
      }
    ]
  },
  {
    type: 'listedCompany',
    title: '上市公司',
    code: 'listedCompany',
    children: [
      {
        title: '主板上市',
        code: 'mainBoardListing'
      },
      {
        title: '港股上市',
        code: 'hkStockListing'
      },
      {
        title: '新三板',
        code: 'newThirdBoard'
      },
      {
        title: '科创版',
        code: 'sciTechInnovationBoard'
      }
    ]
  },
  {
    type: 'intellectualProperty',
    title: '知识产权',
    code: 'intellectualProperty',
    children: [
      {
        title: '查商标',
        code: 'trademarkInquiry'
      },
      {
        title: '查专利',
        code: 'patentInquiry'
      },
      {
        title: '查著作权',
        code: 'copyrightInquiry'
      },
      {
        title: '查网站',
        code: 'websiteInquiry'
      }
    ]
  }
];
let othermapping = {
  风险监控: '',
  查园区: '',
  // '查老板': '/companyPackage/pages/searchsBoss/searchs',
  ...mapping
};

let allListAry = (function (arrs) {
  let arr = [];
  arrs.forEach(item => {
    item?.children?.length &&
      item.children.forEach(i => {
        if (mapping[i.title]) {
          i.is_allow = true;
        }
        i.img = `https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/${i.code}.png`;
        arr.push(i);
      });
  });
  return arr;
})(allList);

let homeMapFn = function (arr) {
  return arr
    .filter(item => mapping[item.title])
    .map((item, idx) => {
      return {
        code: item.code,
        url: mapping[item.title],
        title: item.title,
        img: item.img,
        homeImg: `https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>/${
          codeMap[item.title]
        }.png`,
        sys_code: 'small',
        sort: idx
      };
    });
};
module.exports = {
  allList,
  allListAry,
  othermapping,
  homeMapFn,
  homePageServiceList: homeMapFn(allListAry)
};
