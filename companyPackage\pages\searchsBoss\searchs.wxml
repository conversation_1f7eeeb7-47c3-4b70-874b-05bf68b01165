<import src="/template/null/null"></import>
<import src="/template/more/more"></import>
<import src="/template/loading/index"></import>
<wxs module="tool">
  var isShowCont = function (ent_name) {
    if (ent_name.length == '' && ent_name.length <= 0) { //不是高级搜索的情况-不管登录 
      return true
    } else {
      return false
    }
  }
  var isShowHis = function (ent_name, isLogin) {
    if (ent_name.length > 0 || !isLogin) { //没有登录++有内容  也不显示
      return true
    } else {
      return false
    }
  }
  module.exports = {
    isShowCont: isShowCont,
    isShowHis: isShowHis
  }
</wxs>
<view class="pages">
  <!-- input -->
  <view style="background: #fff;border: 1rpx solid #fff;">
    <view class='searchs'>
      <view class="s-input">
        <view class="s-input-img">
          <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png" mode="aspectFit"></image>
        </view>
        <view class="s-input-item">
          <!--  bindinput="onInput"  -->
          <input class="s-input-item-i" type="text" placeholder='请输入名字关键词' placeholder-class='placeholder' bindblur='onBlur' value="{{ents_name}}" focus="{{inputShowed}}" bindconfirm='onConfirm' confirm-type='search' />
          <view hidden="{{ent_name.length <= 0}}" catchtap="onClear" class="input-clear">
            <view class="clearIcon"></view>
          </view>
        </view>
      </view>
      <view class="search-cancel" bindtap="goBack" bindtap="goBack">取消</view>
    </view>
  </view>
  <!-- 历史记录 -->
  <view class="history_wrap" hidden='{{tool.isShowHis(ent_name, isLogin)}}'>
    <!-- 最近搜索 -->
    <view hidden="{{!(historyList.length>0)}}" class="page__autofit search_a">
      <view class="his_title">
        <text class='his_title_l'>最近搜索</text>
        <view class='his_title_icon' bindtap='handleIcon' data-index='a'>
          <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/delet.png" mode="aspectFit"></image>
        </view>
      </view>
      <view class='his_content'>
        <!-- 内容 -->
        <view class='text-box'>
          <block wx:for="{{historyList}}" wx:key='index'>
            <view class="his_content_item" bindtap='historyTap' data-item='{{item}}'>
              {{item}}
            </view>
          </block>
        </view>
      </view>
    </view>
    <!-- 浏览历史 -->
    <view class="page__autofit search_b" hidden="{{!(browsingHistory.length>0)}}">
      <view class="his_titles">
        <text class='his_title_l'>浏览历史</text>
        <view class='his_title_icon' bindtap='handleIcon' data-index='b'>
          <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/delet.png" mode="aspectFit"></image>
        </view>
      </view>
      <scroll-view scroll-y="true" style="height:{{scrollHeight }}px;">
        <view class='his_content1'>
          <!-- 内容 -->
          <block wx:for="{{browsingHistory}}" wx:key="index">
            <view class='his_content1_item' bindtap="goDetail" data-item="{{item}}">
              <view class="his_content1_item_l">
                <image src="{{item.enterprise_log=='' ||item.enterprise_log=='-' ? 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/image-yonghu.png':item.enterprise_log  }}" binderror="errorFunction" data-index="{{index}}"></image>
                <text>{{item.enterprise_name}}</text>
              </view>
              <text class="his_content1_item-r">{{item.create_time}}</text>
            </view>
          </block>
          <!-- 占位 -->
          <view style="height: 80rpx;">
          </view>
          <view style="height: 80rpx;">
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
  <!-- 搜索内容 hidden="{{ent_name.length =='' }}" 用了组件里面的高度获取不了 为0-->
  <view hidden="{{ tool.isShowCont(ent_name)}}">
    <block>
      <!-- 卡片 -->
      <view wx:if="{{!bazaarIsNull}}">
        <view class="tip" style="height: 48px;">
          共找到<text>{{count || 0}}</text>位相关人员
        </view>
        <scroll-view bindrefresherrefresh="bazaarRefresher" refresher-triggered="{{bazaarIsTriggered}}" refresher-enabled bindscrolltolower="bazaarloadMore" scroll-y style="height: {{cardHeight}}px;background: #f7f7f7; padding-bottom: 20rpx;">
          <view class="bus-card">
            <block wx:if="{{bazaarlist.length>0}}">
              <block wx:for="{{bazaarlist}}" wx:key="index">
                <Card bindhandleTit="handleTit" obj="{{item}}" />
              </block>
            </block>
            <view wx:if="{{bazaarlist.length&&bazaarlist.length<count}}" style="width: 100%;">
              <template is='more' data="{{hasData:bazaarHasData}}"></template>
            </view>
          </view>
        </scroll-view>
      </view>
      <!--暂无数据 -->
      <view wx:if="{{bazaarIsNull}}" style="width: 100%;height: {{scrollHeight}}px;">
        <template is='null'></template>
      </view>
    </block>
  </view>
</view>