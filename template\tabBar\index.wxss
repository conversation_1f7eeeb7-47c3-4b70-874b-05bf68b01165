/* 改变滚动样式 */

.weui-navbar {
  position: relative;
  display: flex;
  align-content: flex-start;
  width: 100%;
  font-size: 28rpx;
  /* background: linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(247, 247, 247, 1) 100%) !important; */
  height: 82rpx;
}


.weui-navbar__item {
  display: flex;
  flex: 1;
  height: 100%;
  text-align: center;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #74798c;
  justify-content: center;
  align-items: center;
}

/* 选中的颜色 */
.weui-navbar__item.weui-bar__item_on {
  position: relative;
  font-weight: 600;
  font-size: 30rpx;
  color: #20263a;
}


/* 这种是占一半的情况  */
.weui-navbar__slider {
  position: absolute;
  left: 0;
  right: 0;
  top: 0rpx;
  width: 100%;
  height: 82rpx;
  /* background-color: #E72410; */
  -webkit-transition: -webkit-transform 0.3s;
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
}

.weui-navbar__slider::after {
  position: absolute;
  content: "";
  width: 60rpx;
  height: 8rpx;
  background: linear-gradient(90deg, #E72410 0%, #4AB8FF 100%);
  border-radius: 8rpx;
  left: 50%;
  bottom: 0rpx;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}

.weui-navbar__title {
  display: inline-block;
  width: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
}

.weui-tab__panel {
  box-sizing: border-box;
  height: 100%;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.weui-tab__panel.active {
  background: #f7f7f7;
}



/* navBar是否滚动 */

.home_tesu {
  position: fixed !important;
  left: 0;
  right: 0;
  z-index: 100;
  background: #ffffff;
}

.home_tesu_on {
  opacity: 1;
}

.home_tesu_off {
  /* opacity: 0; */
  display: none;
}