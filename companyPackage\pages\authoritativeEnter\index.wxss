/* companyPackage/pages/authoritativeEnter/index.wxss */
.box {
  height: 100vh;
  background: #f7f7f7;
  display: flex;
  flex-direction: column;
}

/* 搜索框样式 */
.search_box {
  padding: 30rpx 24rpx;
  background: #fff;
  height: 112rpx;
  flex-shrink: 0;
}

/* 列表容器样式 */
.list_box {
  flex: 1;
  overflow-y: scroll;
  box-sizing: border-box;
}

/* 隐藏滚动条 */
.list_box::-webkit-scrollbar {
  display: none;
  width: 0;
  opacity: 0;
}

/* 榜单类型卡片样式 */
.bg_card {
  width: 750rpx;
  background: #fff;
  padding: 24rpx 20rpx;
  border-top: 24rpx solid #f7f7f7;
}

/* 榜单类型标题样式 */
.bg_tit {
  display: flex;
  align-items: center;
}

.bg_tit image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

.bg_tit text {
  font-weight: 600;
  font-size: 32rpx;
  color: #20263a;
}

/* 榜单列表样式 */
.list {
}

/* 榜单卡片样式 */
.small_cad {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  box-sizing: border-box;
  margin-top: 24rpx;
  width: 218rpx;
  height: 120rpx;
  margin-right: 24rpx;
  padding: 22rpx 20rpx;
}

.small_cad:nth-child(3n) {
  margin-right: 0;
}

/* 榜单标题样式 */
.small_cad .tit {
  font-weight: 400;
  font-size: 28rpx;
  color: #20263a;
  z-index: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 榜单数量样式 */
.small_cad .num {
  font-weight: 600;
  font-size: 24rpx;
  color: #9b9eac;
  z-index: 1;
  margin-top: 8rpx;
}

/* 榜单背景图片样式 */
.small_cad image {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

/* 高亮匹配的文本 */
.highlight {
  color: #e72410;
  font-weight: 600;
}

/* 无数据提示样式 */
.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  color: #9b9eac;
  font-size: 28rpx;
}
