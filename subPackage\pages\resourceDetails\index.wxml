<!--subPackage/pages/resourceDetails/resourceDetails.wxml-->
<import src="./template/head/head"></import>
<import src="/subPackage/template/wxParse/wxParse"></import>
<view class="resource-detail">
  <template is="{{type}}" data="{{...info.detail}}"></template>
  <!-- 创新载体详情 -->
  <block wx:if="{{type === 'INNOVATION_CARRIER'}}">
    <TitleBox title="载体简介">
      <view class="par">
        <template is="wxParse" data="{{wxParseData:introduction.nodes}}" />
      </view>
    </TitleBox>
    <TitleBox title="业务/技术方向">
      <view class="par">
        <template is="wxParse" data="{{wxParseData:direction.nodes}}" />
      </view>
    </TitleBox>
    <TitleBox title="详细区位">
      <ResourcesMap styles="width:100%;height:520rpx;" tabList="{{tabList}}" address="{{fullAddress}}" />
    </TitleBox>
    <TitleBox title="同类推荐">
      <view class="list">
        <view class="item" bindtap="toDetail" id="{{item.id}}" wx:for="{{info.recommend}}" wx:key="id">
          <image class="img" src="{{item.img_url}}"></image>
          <view class="desc">
            <view class="title ellipsis">{{item.bearings_name}}</view>
            <view class="ellipsis">
              <text class="label">载体属地：</text>
              <text>{{item.area}}</text>
            </view>
            <view class="ellipsis">
              <text class="label">载体方向：</text>
              <text>{{item.bearings_direction}}</text>
            </view>
          </view>
        </view>
      </view>
    </TitleBox>
  </block>
  <!-- 行业专家详情 -->
  <block wx:if="{{type === 'INDUSTRY_EXPERTS'}}">
    <TitleBox title="简介">
      <view class="par">
        <template is="wxParse" data="{{wxParseData:introduction.nodes}}" />
      </view>
    </TitleBox>
    <TitleBox title="研究领域">
      <view class="par">
        <template is="wxParse" data="{{wxParseData:territory.nodes}}" />
      </view>
    </TitleBox>
    <TitleBox title="同类人才推荐">
      <view class="list">
        <view class="item" bindtap="toDetail" id="{{item.id}}" wx:for="{{info.recommend}}" wx:key="id">
          <image class="img" src="{{item.img_url}}"></image>
          <view class="desc">
            <view class="title ellipsis">{{item.expert_name}}</view>
            <view class="ellipsis">
              <text class="label">{{item.org}}</text>
            </view>
          </view>
        </view>
      </view>
    </TitleBox>
  </block>
  <!-- 创新转化详情 -->
  <block wx:if="{{type === 'INNOVATION_TRANSFORMATION'}}">
    <TitleBox title="载体简介">
      <view class="par">
        <template is="wxParse" data="{{wxParseData:introduction.nodes}}" />
      </view>
    </TitleBox>
    <TitleBox title="业务/技术方向">
      <view class="par">
        <template is="wxParse" data="{{wxParseData:direction.nodes}}" />
      </view>
    </TitleBox>
    <TitleBox title="创新平台推荐">
      <view class="list">
        <view class="item" bindtap="toDetail" id="{{item.id}}" wx:for="{{info.recommend}}" wx:key="id">
          <image class="img" src="{{item.img_url}}"></image>
          <view class="desc">
            <view class="title ellipsis">{{item.innovate_name}}</view>
            <view class="ellipsis">
              <text class="label">载体方向：</text>
              <text>{{item.innovate_type}}</text>
            </view>
          </view>
        </view>
      </view>
    </TitleBox>
  </block>
  
</view>