@import "/template/more/more.scss";
@import "/template/null/null.scss";
.definitive-list {
  width: 100%;
  background: #f7f7f7;
  height: 100vh;
  overflow: hidden;
  padding-bottom: env(safe-area-inset-bottom);
}
.search-wrapper {
  background: #fff;
  padding: 20rpx 24rpx;
}
.merch-menu {
  height: 96rpx;
  border-top: 2rpx solid #eee;
  background: #ffffff;
}
.list-wrapper {
  margin: 0 24rpx;
  height: calc(100vh - 224rpx);
  overflow: hidden;
}
.list-item {
  height: 220rpx;
  margin: 24rpx 0;
  background: #ffffff;
  border-radius: 8rpx;
}
.item-top {
  padding: 0 32rpx;
  height: 96rpx;
  border-bottom: 2rpx solid #eee;
  display: flex;
  align-items: center;
}
.type_img {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
}
.name {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798c;
}
.item-bottom {
  height: 124rpx;
  padding: 0 28rpx 0 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.item-bottom .label {
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263a;
  line-height: 38rpx;
  width: 100%;
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAALdJREFUSEu11T0OgzAMhuHPmbhYxcpeqRfJykEQgs6sVa7AgZhwlc7F+AeYo/cJkSwTbv5I6g/vkhNjfT0fH+89DoEaB3MPYEugzoscAuNc2p15AaGJIOITXYGIQH33KHIKRBEVEEHUgBcxAR7EDFgRF2BBYgB4AdCAsSX6P+0u4Dcbinj9UzNgiZsBa9wEeOJqwBtXAZH4KRCNi8AVcREYppJB3EtDpNnT8tKfSk5009LX3E5z5gtY2L0ZrugeNQAAAABJRU5ErkJggg==")
    no-repeat right center;
  background-size: 24rpx 24rpx;
}
.arrow_img {
  width: 24rpx;
  height: 24rpx;
  padding-left: 10rpx;
}
.recommend-wrap {
  /* height: 100vh; */
  overflow-x: hidden;
  background-color: #f7f7f7;
}
.null {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 27rpx;
  color: #20263a;
  background-color: #fff;
}
.null .img {
  width: 404rpx;
  height: 204rpx;
  margin-bottom: 38rpx;
}
.text {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798c;
}
.no-margin {
  margin: 0;
  padding: 0;
}

.weui-loadmore {
  width: 65%;
  margin: 28rpx auto;
  line-height: 1.6em;
  font-size: 14px;
  text-align: center;
}

.weui-loadmore__tips {
  color: #9b9eac;
  display: inline-block;
  vertical-align: middle;
}
.input_match {
  color: #e72410 !important;
}
