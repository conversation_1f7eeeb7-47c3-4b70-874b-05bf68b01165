@import "/template/null/null.scss";
@import "/template/more/more.scss";

.fbPage {
  width: 100%;
  height: 100%;
  background: #f7f7f7;
}

.fb-wrap {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  overflow-y: scroll;
  padding: 20rpx 24rpx;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.fbox {
  margin-bottom: 20rpx;
  padding: 24rpx 0;
  background: #fff;
  border-radius: 16rpx;
}

.fb-title {
  padding: 0 24rpx 24rpx;
}

.fb-title-left text:nth-child(1) {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 600;
  color: #20263a;
}
.fb-title-left {
  display: flex;
  align-items: center;
}
.fb-title .status {
  border-radius: 4rpx;
  padding: 0 10rpx;
  height: 36rpx;
  font-size: 24rpx;
  line-height: 36rpx;
  font-weight: 400;
  text-align: center;
  margin-left: 24rpx;
}
.fb-title .status.noover {
  background: rgba(253,147,49,0.1);
  color: #FD9331;
}

.fb-title .status.over {
  background: rgba(38,200,167,0.1);
  color: #26C8A7;
}

.fb-title .time {
  font-size: 24rpx;
  font-family: Inter-Regular, Inter;
  font-weight: 400;
  color: #74798C;
}

.fbox .text {
  padding: 0 24rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #20263a;
}

.img-wrap {
  display: flex;
  margin-top: 24rpx;
  padding: 0 24rpx;
  flex-wrap: wrap;
}

.img-wrap .img {
  width: 160rpx;
  height: 160rpx;
  margin-right: 24rpx;
  margin-bottom: 24rpx;
  border-radius: 5rpx;
  overflow: hidden;
}

.fbox .zhanwei {
  width: 24rpx;
}