import { setTagColor } from '../../../../../utils/util'
Component({
  options: {
    addGlobalClass: true
  },
  properties: {

    obj: { //传进来的数组 ，渲染 -目前是写死的 --通过setTagColor处理一下标签
      type: Object,
      observer(val) {
        // console.log(val)
        val.logo = /(http|https):\/\/([\w.]+\/?)\S*/.test(val.logo) ? val.logo : 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesen.png';
        let tags = []
        //这里现在是单个，还是字符串 不知道后面会怎样
        if (val['status']) {
          tags.push(val['status'])
        }
        if (val['ent_type']) {
          tags.push(val['ent_type'])
        }
        // console.log(tags)
        this.setData({
          tags: tags.length > 0 ? setTagColor(tags) : [],//['世界500强', '续存', '纳税A级企业', '微型', '央企']
          objData: val
        })
      }
    },
    searchKey:{
      type:String,
      value:"",
    }
  },
  data: {
    filterItem:[
      "石化",
      "电力/热力/燃气及水生产和供应",
      "装备制造",
      "新能源",
      "新材料",
      "精细化工",
      "航空",
      "光伏",
      "电子机械",
      "精密机械",
      "汽车及零部件",
      "其他",
      "电子信息",
      "生物医药",
      "医疗器械",
      "智能制造",
      "军民融合"
    ],
    tags: [],
  },
  methods: {
    collect: function (e) {
      const item = e.target.dataset['item'] || e.currentTarget.dataset['item']
      this.triggerEvent('cardFun', item)
    },
    clickTitle(e) {
      let { item } = e.currentTarget.dataset
      this.triggerEvent('goDetail', item)
    }
  }
})
