var behavior = require('../../../../../template/menuhead/index');
var financingBehavior = require('./behaviorConfig');
import {filterItem} from "./filterItem"
import { financingTime } from '../../../../../utils/formate'
Component({
  behaviors: [financingBehavior, behavior],
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多个 slot 支持
  },
  properties: {
    isFocus: { //外面是否聚焦
      type: String,
      observer(val) {
        if (val) {
          this.closeHyFilter()
        }
      }
    },
    regionTop:{
      type:Number,
      value:230
    }
  },
  data: {
    regionData: [], //选中的地区
    title: '开始时间',
    startTime: '', // 开始时间
    endTime: '', // 结束时间
  },
  observers: {
    // regionData: function (list) {
    //   if (list.length > 0) {
    //     this.setData({ source_val: false })
    //   }
    // },
  },
  lifetimes: {
    ready() {
      this._getFilterItems();
    },
    created(){ },
  },
  methods: {
    close(e) {
      this.closeHyFilter()
    },
    // 所属产业 
    getbusness(e) {
      let { source_val } = this.data
      const detail = e.detail[0]
      
      let data = detail.codeList.length && JSON.parse(JSON.stringify(detail.codeList)) || [];
      console.log(77843821342,data)
      if (data.length > 0) {
        source_val = true
      } else {
        source_val = false
      }
      
      this.setData({ 'leadingIndustryCode.cur': data, source_val }, () => {
        this.backAll()
      });
    },
    // 地区 
    getRegion(e) {
      console.log('e', e);
      let {
        district_val,
      } = this.data;
      let data = e.detail[e.detail.length-1].code; //解決checked狀態丟失的情況--拷貝--切記
      if (data) {
        district_val = true
      } else {
        district_val = false
      }
      this.setData({
        regionData:data,
        district_val,
      }, () => this.backAll());
    },
    // 融资轮次 
    getfinancing(e) {
      let { filter_val } = this.data;
      const detail = e.detail[0]
      let data = detail.codeList.length && JSON.parse(JSON.stringify(detail.codeList)) || [];
      if (data.length > 0) {
        filter_val = true
      } else {
        filter_val = false
      }
      this.setData({ 'parkLevel.cur': data, filter_val }, () => {
        this.backAll()
      })
    },
    // 更多筛选
    handleMoreFilter(e) {
      let { four_val } = this.data;
      const detail = e.detail
      detail.map((item) => {
        let data = item?.codeList && JSON.parse(JSON.stringify(item.codeList)) || [];
        this.setData({ [item.id]: {...this.data[item.id], cur: data, four_val} });
      });
      four_val =  detail.some((data) => data.codeList.length > 0)
      this.setData({ four_val }, () => this.backAll())
    },
    // 总的返回
    backAll() {
      const requestData = {
        leadingIndustryCode: '',
        parkLevel: '',
        region: '',
      };
      const {
        regionData,
        leadingIndustryCode,
        parkLevel,
        serviceListCode,
        policyCheckListCode,
        parkNature
      } = this.data;
      console.log(333444,leadingIndustryCode.cur)
      // 业务领域 ---這裏有點特殊
      if (leadingIndustryCode.cur.length) {
        requestData['leadingIndustryCode'] = filterItem.leading_industry_code.indexOf(leadingIndustryCode.cur[0]) 
      }else{
        requestData['leadingIndustryCode']=''
      }
      // 融资轮次
      if (parkLevel.cur.length) {
        requestData['parkLevel'] =filterItem.park_level.indexOf(parkLevel.cur[0])  
      }else{
        requestData['parkLevel']=''
      }
      
      // 所处阶段
      if (policyCheckListCode.cur.length) {
        requestData['policyCheckListCode'] =filterItem.policy_check_list_code.indexOf(policyCheckListCode.cur[0]) 
      }else{
        requestData['policyCheckListCode']=''
      }

      // 产业优选
      if (serviceListCode.cur.length) {
        requestData['serviceListCode'] =filterItem.service_list_code.indexOf(serviceListCode.cur[0])  
      }else{
        requestData['serviceListCode']=''
      }

      // 融资时间 
      if (parkNature.cur.length) {
        requestData['parkNature'] =filterItem.park_nature.indexOf(parkNature.cur[0])
      }else{
        requestData['parkNature']=''
      }

      // 地区

      if (regionData) {
        requestData['region'] = regionData.toString().replace(/(0+)$/g,"")
      }
      this.closeHyFilter();
      this.triggerEvent('submit', requestData)
    },
    // 自定义日期额外操作
    handlefinancingTime(e) {
      this.setData({
        'financingTime.startTime': '',
        'financingTime.endTime': ''
      })
    },
    // 打开日期弹窗
    showDatePicker(e) {
      const type = e.currentTarget.dataset.type
      const { financingTime } = this.data
      if (type == 'startTime') {
        this.setData({
          showDatePicker: true,
          dateType: type,
          date: financingTime.startTime,
          title: '开始时间'
        })
      } else {
        this.setData({
          showDatePicker: true,
          dateType: type,
          date: financingTime.endTime,
          title: '结束时间'
        })
      }
    },
    //获取时间
    setDate(e) { 
      console.log('e', e);
      const { date } = e.detail
      if(!date.includes(undefined)) {
        const { dateType, financingTime} = this.data
        financingTime.list.map((item) => {
          item.status = '' ;
          return item
        })
        if (dateType == 'startTime') {
          this.setData({
            'financingTime.startTime': date,
          })
        } else {
          this.setData({
            'financingTime.endTime': date,
          })
        }
      }
    },
  },
})
