// companyPackage/pages/authoritativeEnter/index.js
const app = getApp();
Page({
  data: {
    keyword: '',
    // mock数据
    rankTypes: [{
        id: 1,
        name: '综合榜',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/definitive/type_icon.png',
        lists: [{
            id: 101,
            title: '企业榜',
            count: 1567
          },
          {
            id: 102,
            title: '人物榜',
            count: 982
          },
          {
            id: 103,
            title: '产品榜',
            count: 2345
          },
          {
            id: 104,
            title: '创新榜',
            count: 876
          },
          {
            id: 105,
            title: '科技榜',
            count: 1234
          }
        ]
      },
      {
        id: 2,
        name: '行业榜',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/definitive/type_icon.png',
        lists: [{
            id: 201,
            title: '互联网榜',
            count: 1245
          },
          {
            id: 202,
            title: '金融榜',
            count: 867
          },
          {
            id: 203,
            title: '医疗榜',
            count: 543
          },
          {
            id: 204,
            title: '教育榜',
            count: 789
          },
          {
            id: 205,
            title: '制造业榜',
            count: 1023
          }
        ]
      }
    ],
    // 过滤后的数据
    filteredRankTypes: []
  },

  onLoad() {
    // 初始化时将所有数据设置到filteredRankTypes
    this.setData({
      filteredRankTypes: this.data.rankTypes
    });
  },
  // 点击跳转到榜单页面 
  goDetail(e) {
    const {
      dataset
    } = e.currentTarget;
    console.log(dataset.item);
    // ?pay_code=${obj.pay_code}&money=${obj.money}
    app.route(this, `/companyPackage/pages/authoritativeList/authoritativeList`);
  },

  // 处理输入框输入
  handleInput(e) {
    const keyword = e.detail;

    this.setData({
      keyword
    });
    this.filterRankData(keyword);
  },

  // 过滤并高亮匹配的数据
  filterRankData(keyword) {
    if (!keyword) {
      // 如果关键词为空，显示所有数据且不高亮
      this.setData({
        filteredRankTypes: this.data.rankTypes
      });
      return;
    }

    // 复制原始数据进行处理
    const filteredData = this.data.rankTypes
      .map(type => {
        // 深拷贝榜单类型对象
        const newType = {
          ...type
        };

        // 处理榜单列表
        const matchedLists = type.lists
          .map(list => {
            // 检查榜单标题是否匹配
            const isMatched = list.title.includes(keyword);

            if (isMatched) {
              // 只有匹配到的才需要处理高亮
              return {
                ...list,
                highlightedTitle: this.highlightKeyword(list.title, keyword),
                matched: true
              };
            }

            return {
              ...list,
              matched: false
            };
          })
          .filter(item => item.matched);

        // 返回处理后的榜单类型
        newType.lists = matchedLists;
        newType.matched = matchedLists.length > 0;

        return newType;
      })
      .filter(type => type.matched);

    this.setData({
      filteredRankTypes: filteredData
    });
  },

  // 高亮关键词
  highlightKeyword(text, keyword) {
    if (!keyword || !text.includes(keyword)) {
      return [{
        text,
        highlight: false
      }];
    }

    // 将文本分割成匹配和非匹配部分
    const parts = [];
    let lastIndex = 0;
    let index = text.indexOf(keyword);

    while (index !== -1) {
      // 添加非匹配部分
      if (index > lastIndex) {
        parts.push({
          text: text.substring(lastIndex, index),
          highlight: false
        });
      }

      // 添加匹配部分
      parts.push({
        text: text.substring(index, index + keyword.length),
        highlight: true
      });

      lastIndex = index + keyword.length;
      index = text.indexOf(keyword, lastIndex);
    }

    // 添加剩余的非匹配部分
    if (lastIndex < text.length) {
      parts.push({
        text: text.substring(lastIndex),
        highlight: false
      });
    }

    return parts;
  }
});