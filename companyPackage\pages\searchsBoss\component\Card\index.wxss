.box {
  width: 750rpx;
  /* height: 632rpx; */
  background: #FFFFFF;
  margin-bottom: 20rpx;
  padding-bottom: 32rpx;
}

.head {
  padding: 32rpx 0 24rpx;
  margin: 0 32rpx;
  border-bottom: 1rpx solid #EEEEEE;
  display: flex;
}

.head-l {
  width: 96rpx;
  height: 96rpx;
  flex-shrink: 0;
}

.head-r {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 20rpx;
}

/* .head-r view:nth-of-type(1) {
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #E72410;
  line-height: 38rpx;
} */

.card_h_l {
  width: 420rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  flex-wrap: nowrap;
}

.card_h_l .text {
  display: inline;
  font-size: 32rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263A;
}

.card_h_l .activetext {
  color: #E72410 !important;
}


.head-r-b {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  line-height: 33rpx;
}

.head-r-b text {
  color: #E72410;
  padding: 0 4rpx;
}

.list {
  /* height: 208rpx; */
  border-bottom: 1rpx solid #eee;
  padding: 24rpx 0 4rpx;
  margin: 0 32rpx;
}

.list-i {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40rpx;
  margin-bottom: 24rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
}

.list-i-l {
  flex-shrink: 0;
}

.list-i-l text:nth-of-type(1) {
  color: #20263A;
}

.list-i-l text:nth-of-type(2) {
  color: #E72410;
}

.list-i-r {
  width: 392rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  text-align: right;
}

.partner-t {
  display: flex;
  align-items: center;
  height: 80rpx;
  padding-left: 32rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
}

.partner_box {
  height: 160rpx;
  width: 100%;
  display: flex;
  padding-left: 32rpx;
  scroll-behavior: smooth;
  overflow-x: scroll;
}

.partner_box .box {
  width: 316rpx;
  height: 160rpx;
  background: #F7F7F7;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  overflow: hidden;
  margin-right: 20rpx;
  padding: 16rpx;

}

.box_t {
  display: flex;
}

.box_l {
  width: 72rpx;
  height: 72rpx;
  flex-shrink: 0;
}

.box_r {
  margin-left: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.box_r view:nth-of-type(1) {
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 200rpx;
}

.box_r view:nth-of-type(2) {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
}

.box_r view:nth-of-type(2) text {
  color: #E72410;
  padding: 0 4rpx;
}

.box_b {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  padding-top: 20rpx;
}

.more {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.more .img {
  width: 48rpx;
  height: 48rpx;
}

.more view {
  height: 36rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  padding-top: 8rpx;
}