import {
  home
} from '../../../service/api';
import {
  formatDate,
  getOldTime
} from '../../../utils/formate'
import {
  getShareUrl
} from '../../../utils/mixin/pageShare'

var WxParse = require('../../template/wxParse/wxParse.js');
const app = getApp()

Page({
  data: {
    isIphoneX: app.globalData.isIphoneX,
    item: {},
    aboutList: [],
    tab: [{
        name: '政策原文',
        code: 'a'
      },
      {
        name: '政策解读',
        code: 'b'
      },
    ],
    curCode: 'a'
  },
  onLoad: function (options) {
    let id = options?.id || false
    const hpolicyId = options?.hpolicyId || false
    // console.log(options)
    if (hpolicyId) {
      id = hpolicyId;
    }
    if (!id) return
    this.setData({
      id
    }) //() => this.request()
  },
  onShow() {
    const {
      id
    } = this.data
    if (id) this.request();
  },
  onUnload() {
    // let route = getCurrentPages()
    // if (route.length) {
    //   let routes = route.filter(item => item.route === 'pages/home/<USER>')
    //   routes.length && routes[0].getInit()
    // }
    if (this.data.share) {
      wx.switchTab({
        url: '/pages/home/<USER>',
      })
    }
  },
  request() {
    this.init()
    this.about()
  },
  async about() {
    let {
      id
    } = this.data
    try {
      // const arr = await home.introductList(id)
      const arr = await home.hqdPolicyhotNews()
      // console.log(arr)
      arr.forEach(item => {
        item.create_time = formatDate(item.issuing_time, 'yyyy-MM')
      })
      this.setData({
        aboutList: arr
      })
    } catch (err) {
      console.log(err)
    }
  },
  async init(isLoading) {
    let that = this;
    const {
      id
    } = that.data
    try {
      // let obj = await home.introductionDetail(id, isLoading)
      let obj = await home.hqdPolicyDetail(id, isLoading)
      obj.create_time = formatDate(obj.issuing_time, 'yyyy-MM')
      obj.tag_name = obj.tag_name.split(',').slice(0, 3)
      obj.accessory = obj.accessory.split(',')
      obj.accessory_name = obj.accessory_name.split(',')
      WxParse.wxParse('courseDetailOne', 'html', obj.original_text, that, 5)
      WxParse.wxParse('courseDetailTwo', 'html', obj.unscramble, that, 5)
      this.setData({
        item: obj
      })
    } catch (err) {
      // app.showToast("未知错误，请稍后再试！", "none", 2000)
      // setTimeout(() => {
      //   wx.navigateBack({
      //     delta: 1,
      //   })
      // }, 2000)
      // console.log(err)
    }
  },
  onClick(e) {
    const that = this;
    const type = e.currentTarget.dataset.item
    const {
      id,
      item
    } = this.data
    const {
      login
    } = app.globalData
    switch (type) {
      case 'praise': //点赞
        if (!login) {
          app.route(that, '/pages/login/login')
          return
        };
        home.introductzan(id).then(res => {
          this.init(true)
        }).catch(err => {
          console.log(err)
        })
        break;
      case 'collect': //收藏
        if (!login) {
          app.route(that, '/pages/login/login')
          return
        };
        // 收藏接口没得
        // home.introductshouc(id).then(res => {
        //   this.init(true)
        // }).catch(err => {
        //   console.log(err)
        // })
        break;
      case 'news':
        var query = wx.createSelectorQuery() //创建节点查询器
        query.selectViewport().scrollOffset(); //节点的竖直滚动位置
        query.select(`#news`).boundingClientRect() //动态获取id
        query.exec(function (res) { //执行请求
          // console.log(res)
          // return
          wx.pageScrollTo({
            scrollTop: res[0].scrollTop + res[1].top, //滚动到页面节点的上边界坐标
            duration: 300 // 滚动动画的时长
          });
        })

        break;

      default:
        break;
    }
  },
  checkTab(e) {
    const code = e.currentTarget.dataset.code
    this.setData({
      curCode: code
    })
  },
  onShareAppMessage: function (res) {
    const that = this;
    const {
      id
    } = this.data
    const {
      login
    } = app.globalData;
    if (login) {
      return {
        title: '政策详情',
        path:getShareUrl(`/pages/home/<USER>
        success: function (res) {
          console.log('成功', res)
        }
      }
    } else {
      app.route(that, '/pages/login/login')
      return Promise.reject();
    }
  },

  // 下载并打开excel文件 
  openExcel(e) {
    const fs = wx.getFileSystemManager()
    let {
      link,
      name
    } = e.currentTarget.dataset
    if (!link) {
      app.showToast('暂无数据!', 'none', 1000)
      return
    }
    app.showLoading()
    //下载文件
    wx.downloadFile({
      url: encodeURI(link),
      success(res) {
        if (res.statusCode === 200) {
          const filePath = res.tempFilePath
          if (/\.rar$/.test(link)) {
            wx.setClipboardData({
              data: link,
              success(res) {
                console.log('打开文档失败')
                wx.getClipboardData({
                  success(res) {
                    // console.log(res.data) // data
                    app.showToast('复制成功,请打开浏览器查看!', 'none', 1000)
                  }
                })
              }
            })
            return
          }
          wx.openDocument({
            filePath: filePath,
            showMenu: true, //关键点
            success: function (res) {
              console.log('打开文档成功')
            },
            fail: function () {
              wx.hideLoading()
              wx.setClipboardData({
                data: link,
                success(res) {
                  console.log('打开文档失败')
                  wx.getClipboardData({
                    success(res) {
                      // console.log(res.data) // data
                      app.showToast('复制成功,请打开浏览器查看!', 'none', 1000)
                    }
                  })
                }
              })
            }
          })
        }
      },
      fail: () => {
        wx.hideLoading()
        wx.setClipboardData({
          data: link,
          success(res) {
            console.log('打开文档失败')
            wx.getClipboardData({
              success(res) {
                // console.log(res.data) // data
                app.showToast('复制成功,请打开浏览器查看!', 'none', 1000)
              }
            })
          },
        })
      }
    })
  }
})