<template name="capital">
  <view class="sci_heads caphd">
    <view class="nav" capture-catch:touchmove="preventdefault">
      <view class="capitalNum">
        共找到<text>{{capitalCount}}</text>个产业基金
      </view>
      <view class="nav-child {{(district_open || district_val) ? 'active' : ''}}" bindtap="capitalRapDistrictNav" data-nav="1">
        <!-- 文字样式两种-一种是有选择高亮--关闭但是又数据也要高亮 -->
        <view class="nav-title">
          {{selected_source_name.length > 0 ?selected_source_name : dropDownMenuTitle[0]}}
        </view>
        <view class="icona flex_all_center " wx:if="{{district_open}}">
          <image src="{{icon || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-up.png' }}"></image>
        </view>
        <!-- 关闭下拉---还有两种情况 是否 -->
        <view wx:else class="icona  flex_all_center {{activeIcon ? 'iconb' :''}}">
          <image src="{{activeIcon || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-down.png'}}" wx:if="{{!district_val}}"></image>
          <image src="{{activeIcon || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-down-a.png'}}" wx:if="{{district_val}}"></image>
        </view>
      </view>

      <!-- <view class="nav-child  {{(source_open||source_val) ? 'active' : '' }}" bindtap="capitalRapSourceNav" data-nav="2">
        <view class="nav-title">{{selected_source_name.length > 0 ?selected_source_name : dropDownMenuTitle[1]}}
        </view>
        <view class="icona flex_all_center " wx:if="{{source_open}}">
          <image src="{{icon || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-up.png' }}"></image>
        </view>
        <view wx:else class="icona  flex_all_center {{activeIcon ? 'iconb' :''}}">
          <image src="{{activeIcon || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-down.png'}}" wx:if="{{!source_val}}"></image>
          <image src="{{activeIcon || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-down-a.png'}}" wx:if="{{source_val}}"></image>
        </view>
      </view>

      <view class="nav-child {{(filter_open || filter_val) ? 'active' : ''}}" bindtap="capitalTapFilterNav" data-nav="3" wx:if="{{dropDownMenuTitle[2]}}">
        <view class="nav-title">{{selected_filter_name.length > 0 ?selected_filter_name : dropDownMenuTitle[2]}}
        </view>
        <view class="icona flex_all_center " wx:if="{{filter_open}}">
          <image src="{{icon || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-up.png' }}"></image>
        </view>
        <view wx:else class="icona  flex_all_center {{activeIcon ? 'iconb' :''}}">
          <image src="{{activeIcon || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-down.png'}}" wx:if="{{!filter_val}}"></image>
          <image src="{{activeIcon || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-down-a.png'}}" wx:if="{{filter_val}}"></image>
        </view>
      </view>
      <view class="nav-child {{(four_open || four_val) ? 'active' : ''}}" bindtap="policyFourFilterNav" data-nav="4" wx:if="{{dropDownMenuTitle[3]}}">
        <view class="nav-title">{{selected_filter_name.length > 0 ?selected_filter_name : dropDownMenuTitle[3]}}
        </view>
        <view class="icona flex_all_center " wx:if="{{four_open}}">
          <image src="{{icon || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-up.png' }}"></image>
        </view>
        <view wx:else class="icona  flex_all_center {{activeIcon ? 'iconb' :''}}">
          <image src="{{activeIcon || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-down.png'}}" wx:if="{{!four_val}}"></image>
          <image src="{{activeIcon || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-down-a.png'}}" wx:if="{{four_val}}"></image>
        </view>
      </view> -->
    </view>
  </view>
</template>