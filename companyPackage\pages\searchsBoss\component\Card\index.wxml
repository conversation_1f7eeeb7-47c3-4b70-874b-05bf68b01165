<view>
  <view class="box">
    <view class="head">
      <image src="{{objData.pic=='' ?'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/image-yonghu.png':objData.pic  }}" class="head-l" binderror="errorFunction"></image>
      <view class="head-r">
        <view class="card_h_l" bindtap="goDetail">
          <view wx:for="{{objData.myName}}" wx:key="index" class="text {{item==objData.heightKey&&'activetext'}}">{{item}}</view>
        </view>
        <view class="head-r-b">
          关联企业<text>{{objData.relate_count || 0}}</text>家，分布情况：
        </view>
      </view>
    </view>
    <view class="list" wx:if="{{objData.related_group.length}}">
      <block wx:for="{{objData.related_group}}" wx:key="idx" wx:for-item="itm" wx:for-index="idx">
        <view class="list-i">
          <view class="list-i-l">
            <text>{{itm.name}}</text>（共<text>{{itm.num || 0}}</text>家）
          </view>
          <view class="list-i-r">
            {{itm.ent_name}}
          </view>
        </view>
      </block>
    </view>
    <!-- 主要合作伙伴 -->
    <view wx:if="{{objData.coop_partner.length}}">
      <view class="partner-t">
        主要合作伙伴
      </view>
      <view class="partner_box">
        <view style="display: flex;">
          <block wx:for="{{objData.coop_partner}}" wx:key="indx" wx:for-item="i" wx:for-index="indx">
            <view class="box">
              <view class="box_t" bindtap="goDetail" data-item="{{i}}" data-type="son">
                <image src="{{i.person_pic==''? 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/image-yonghu.png':i.person_pic  }}" class="box_l" binderror="errorFunctions" data-index="{{indx}}"></image>
                <view class="box_r">
                  <view>
                    {{i.name}}
                  </view>
                  <view>
                    合作<text>{{i.count || 0}}</text>次
                  </view>
                </view>
              </view>
              <view class="box_b">
                {{i.ent_name}}
              </view>
            </view>
          </block>
          <!-- 查看更多 -->
          <view class="box more" bindtap="goPartner" wx:if="{{objData.coop_partner.length>4}}">
            <image src="/image/boss/bmore.png" class="img"></image>
            <view>
              查看更多
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>