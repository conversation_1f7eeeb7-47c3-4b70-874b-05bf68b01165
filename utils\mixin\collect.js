import {
  common
} from '../../service/api'
const app = getApp()

const collect = async (that, detailData, listName) => {
  if (!app.globalData.login) {
    app.route(this, '/pages/login/login')
    return
  }
  console.log('收藏', detailData)
  let comDetail = JSON.parse(JSON.stringify(detailData))
  let list = that.data[listName]
  const index = list.findIndex(item => item.ent_id == comDetail.ent_id)
  if (comDetail.collect) {
    // 收藏的情况
    let cancelRes = await common.cancelCollect({
      ent_id: comDetail.ent_id,
      // ent_name: Array.isArray(comDetail.ent_name) ? comDetail.ent_name.join('') : comDetail.ent_name ? comDetail.ent_name : ''
    })
    if (cancelRes?.message != 'SUCCESS') {
      wx.showToast({
        title: '取消收藏失败!',
        icon: 'none'
      })
      return
    }
    wx.showToast({
      title: '已取消收藏',
      icon: 'none'
    })
    list[index].collect = false
    that.setData({
      [`${listName}`]: list
    })
  } else {
    // 未收藏
    let allList = await common.contact(comDetail.ent_id, encodeURI('types=1,2'));
    let tags = ''
    tags = comDetail?.tags && comDetail?.tags?.length ? comDetail.tags.join(',') : ''
    console.log('222', tags)
    let comData = {
      ent_id: comDetail.ent_id,
      ent_name: Array.isArray(comDetail.ent_name) ? comDetail.ent_name.join('') : comDetail.ent_name ? comDetail.ent_name : '',
      logo: comDetail?.logo || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png',
      tags: tags,
      public_contact: allList?.length ? true : false,
    }
    let collectRes = await common.collectCompanytoProject(comData)
    list[index].collect = true
    that.setData({
      [`${listName}`]: list
    })
    wx.showToast({
      title: '收藏成功',
      icon: 'success'
    })
  }
}
const collectWithoutList = async (that, detailData) => {
  if (!app.globalData.login) {
    app.route(this, '/pages/login/login')
    return
  }
  let comDetail = JSON.parse(JSON.stringify(detailData))
  if (comDetail.collect) {
    // 收藏的情况
    let cancelRes = await common.cancelCollect({
      ent_id: comDetail.ent_id,
      // ent_name: Array.isArray(comDetail.ent_name) ? comDetail.ent_name.join('') : comDetail.ent_name ? comDetail.ent_name : ''
    })
    if (cancelRes?.message != 'SUCCESS') {
      wx.showToast({
        title: '取消收藏失败!',
        icon: 'none'
      })
      return
    }
    wx.showToast({
      title: '已取消收藏',
      icon: 'none'
    })
  } else {
    // 未收藏
    let allList = await common.contact(comDetail.ent_id, encodeURI('types=1,2'));
    let tags = ''
    tags = comDetail?.tags && comDetail?.tags?.length ? comDetail.tags.join(',') : ''
    console.log('222', tags)
    let comData = {
      ent_id: comDetail.ent_id,
      ent_name: Array.isArray(comDetail.ent_name) ? comDetail.ent_name.join('') : comDetail.ent_name ? comDetail.ent_name : '',
      logo: comDetail?.logo || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png',
      tags: tags,
      public_contact: allList?.length ? true : false,
    }
    let collectRes = await common.collectCompanytoProject(comData)
    wx.showToast({
      title: '收藏成功',
      icon: 'success'
    })
  }
}

module.exports = {
  collect,
  collectWithoutList
}