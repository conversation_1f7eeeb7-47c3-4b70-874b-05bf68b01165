/* automobile/pages/childrenpage/carrierDetail/index.scss */
@import '/template/null/null.scss';

.carrier-detail-wrapper {
  background: #F7F7F7;
  height: 100%;
}

/* 顶部企业相关信息 */
.top-content-wrapper {
  padding: 40rpx 24rpx;
  background: #fff;
}

.top-content-wrapper .top-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.top-content-wrapper .top-content>image {
  width: 136rpx;
  height: 136rpx;
  background: #ccc;
}

.top-content-wrapper .top-content .company-detail {
  width: calc(100% - 160rpx);
}

.company-detail .title {
  line-height: 56rpx;
  font-size: 36rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
}

.company-detail .info {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 16rpx;
}

.company-detail .info image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.company-detail .info .type-line {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.company-detail .info .type-line .line {
  width: 2rpx;
  height: 32rpx;
  margin: 0 24rpx;
  background: #aaa;
}

.company-detail .info .type-line text {
  line-height: 32rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #3D4255;
}

/* 企业详细信息表 */
.info-content .info-item {
  width: 100%;
  background: #fff;
  margin-top: 8rpx;
  word-break: break-all;
  display: flex;
  justify-content: flex-start;
}

.info-content .info-item ._title {
  display: inline-block;
  min-width: 120rpx;
  line-height: 34rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
}

.info-content .info-item ._content {
  line-height: 34rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #3D4255;
}

/* 页面主体板块 */
.main-content-wrapper {
  width: 100%;
  background: #F7F7F7;
}

/* 通用---板块标题 */
.main-content-wrapper .plate-title {
  padding: 28rpx 24rpx;
  background: #fff;
  margin-top: 22rpx;
  border-bottom: 2rpx solid #EEE;
}

.main-content-wrapper .plate-title .title {
  line-height: 42rpx;
  font-size: 30rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
}

.main-content-wrapper .main-content {
  background: #fff;
  padding: 28rpx 24rpx;
}

.main-content-wrapper .main-content.none-padding {
  padding: 0;
}