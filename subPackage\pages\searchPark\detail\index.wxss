.erweicode_qq {
  height: 420rpx;
}

.filterContainer {
  position: relative;
}

.bottom_text {
  width: 100%;
  position: absolute;
  bottom: 10rpx;
  display: flex;
  justify-content: center;
}

.color_yuan {
  margin-left: 12rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 16rpx;
  background-color: #EDEDEC;
}

.color_yuan1 {
  background-color: #E72410;
}

.header {
  padding: 40rpx 24rpx 24rpx 32rpx;
  border-bottom: 20rpx #F7F7F7 solid;
}

.title {
  font-size: 40rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  line-height: 47rpx;
}

.tag {
  display: flex;
  margin-top: 20rpx;
}

.tag-item {
  background: rgba(253, 147, 49, 0.1);
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  padding: 4rpx 10rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #FD9331;
  line-height: 28rpx;
  margin-right: 20rpx;
}

.parameter {
  margin-top: 32rpx;
  background-color: #F7F7F7;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  display: flex;
  justify-content: space-between;
  padding: 24rpx;
}

.parameter-item {
  padding-right: 24rpx;
  border-right: 2rpx solid #EEEEEE;
}

.parameter-item:last-child {
  border-right: 0px;
}

.parameter-item>view:nth-child(1) {
  text-align: center;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  line-height: 28rpx;
  margin-bottom: 8rpx;
}

.parameter-item>view:last-child {
  text-align: center;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 40rpx;
}

.intro {
  margin-top: 32rpx;
}

.fnc {
  width: 160rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  line-height: 33rpx;
}

.industry {
  display: flex;
  margin-top: 24rpx;
}

.industry>view:last-child {

  display: -webkit-box;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 40rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  width: 562rpx;
  height: 40rpx;
}
.industry>view:last-child text{
  margin-right: 20rpx;
}
.profile {
  padding-bottom: 20rpx;
  border-bottom: 20rpx #F7F7F7 solid;
}

.profile-title {
  padding: 24rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  line-height: 38rpx;
  border: 1rpx solid #EEEEEE;
}

.profile-item {
  padding: 32rpx 24rpx;
}

.profile-item-title {
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  line-height: 33rpx;
  margin-bottom: 8rpx;
}

.profile-item-nav {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  line-height: 46rpx;
}

.body {
  padding-bottom: 140rpx;
}

.bottom {
  width: 100%;
  background-color: #fff;
  padding: 24rpx;
  padding-bottom: 68rpx;
  position: fixed;
  bottom: 0rpx;
}

.button {
  height: 88rpx;
  width: 100%;
  text-align: center;
  font-size: 32rpx;
  padding-top: 15rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 38rpx;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  background: -webkit-linear-gradient(left, rgba(245, 110, 96, 1), rgba(231, 36, 16, 1));
}
.button-empty{
  background: #74798C;
}