// pages/vant-demo/vant-demo.js
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';

Page({
  data: {
    searchValue: '',
    activeTab: 0,
    pickerShow: false,
    pickerColumns: [
      ['杭州', '宁波', '温州', '绍兴', '湖州'],
    ],
    pickerValue: '',
    popupShow: false,
    fieldValue: '',
    dropdownValue: 0,
    dropdownOptions: [
      { text: '全部商品', value: 0 },
      { text: '新款商品', value: 1 },
      { text: '活动商品', value: 2 },
    ],
  },

  onLoad() {
    console.log('Vant Demo 页面加载');
  },

  // 搜索相关
  onSearchChange(event) {
    this.setData({
      searchValue: event.detail
    });
  },

  onSearchConfirm() {
    Toast('搜索：' + this.data.searchValue);
  },

  onSearchClear() {
    this.setData({
      searchValue: ''
    });
  },

  // Tab 切换
  onTabChange(event) {
    this.setData({
      activeTab: event.detail.index
    });
    Toast(`切换到标签 ${event.detail.index + 1}`);
  },

  // 按钮点击事件
  onButtonClick() {
    Toast.success('按钮点击成功！');
  },

  onButtonLoading() {
    Toast.loading({
      message: '加载中...',
      forbidClick: true,
      duration: 2000
    });
  },

  // 弹窗相关
  showPopup() {
    this.setData({
      popupShow: true
    });
  },

  closePopup() {
    this.setData({
      popupShow: false
    });
  },

  // 对话框
  showDialog() {
    Dialog.confirm({
      title: '提示',
      message: '这是一个确认对话框',
    }).then(() => {
      Toast.success('确认');
    }).catch(() => {
      Toast.fail('取消');
    });
  },

  // 选择器
  showPicker() {
    this.setData({
      pickerShow: true
    });
  },

  onPickerConfirm(event) {
    const { value } = event.detail;
    this.setData({
      pickerValue: value,
      pickerShow: false
    });
    Toast(`选择了：${value}`);
  },

  onPickerCancel() {
    this.setData({
      pickerShow: false
    });
  },

  // 输入框
  onFieldChange(event) {
    this.setData({
      fieldValue: event.detail
    });
  },

  // 下拉菜单
  onDropdownChange(event) {
    this.setData({
      dropdownValue: event.detail
    });
    const option = this.data.dropdownOptions[event.detail];
    Toast(`选择了：${option.text}`);
  },

  // Cell 点击
  onCellClick() {
    Toast('Cell 被点击');
  }
});
