<template name="nav">
  <view class="weui-navbar">
    <block wx:for="{{tabs}}" wx:key="title">
      <view id="{{index}}" class="weui-navbar__item {{'weui-navbar__item'+index}} {{activeIndex == index ? 'weui-bar__item_on' : ''}}" bindtap="tabClick" data-title="{{item.title}}">
        <view class="weui-navbar__title">{{item.title}}</view>
      </view>
    </block>
    <view class="weui-navbar__slider" style="left: {{sliderLeft}}px; transform: translateX({{sliderOffset}}px); -webkit-transform: translateX({{sliderOffset}}px); width: {{sliderWidth}}px;">
    </view>
  </view>
</template>