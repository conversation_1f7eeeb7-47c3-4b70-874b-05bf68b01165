import {
    chainUrl
} from '../../../../../service/config';
import request from '../../../../../service/request'
import {
    common
} from '../../../../../service/api';
const app = getApp()
Component({
    /**
     * 组件的属性列表
     */
    properties: {

        // 弹出层显示/隐藏
        visible: {
            type: Boolean,
            value: false,
        },
        // 是否显示第三级
        isShowThird: {
          type: Boolean,
          value: true
        },
        top:{
          type:Number,
          value:230
        },
        zIndex: {
            type: Number,
            value: 80
        },
        // 上一次选中的数据，用于回填
        oldData: {
            type: String,
        },
        headText: {
            type: String,
            value: '全部'
        },
        storageObj: { //防止后面存储本地内容冲突
            type: Object,
            require: true,
            value: {
                regionList: 'districtAry',
                codeList: 'codeList-b'
            }
        }
    },

    /**
     * 组件的初始数据
     */
    data: {
        regionList: [], //  地区列表
        codeList: [], // 存储点击过的省市code
        checkedList: [], // 存储选中项
        provinceList: [],
        cityList: []
    },

    lifetimes: {
        attached() {
            if (!this.data.storageObj) return
           
            let list =wx.getStorageSync(this.data.storageObj.regionList)
           if(typeof(list)=='string'){
            list=JSON.parse(list)
           }
           if(list[0].name!='全部'){
            list.unshift({code: "",id: 1234,level: "1",name: "全部",parent: "",children:[]})
           }
            const regionList =list // 后面要注意 有两个不一样的地区选择组件
            const codeList =wx.getStorageSync(this.data.storageObj.codeList) ||[];
            if (regionList) {
                !this.data.isShowThird && this.setBackFill(regionList,[])
                this.setData({
                    regionList,
                    codeList
                }, () => this.gsetCityAry())
            } else {
                this.getRegion();
            }

        },
    },
    /**
     * 组件的方法列表
     */
    methods: {
         // 禁止遮罩层滚动穿透
    _disabledPenetrate() {
      return
    },
        // 弹出层关闭时清空数据选中状态
        close() {
            this.setData({
                visible: false
            });
            this.triggerEvent('close') // 向外传递一个自定义事件
        },

        // 数据回填
        async backFillRegion() {
            let {
                regionList,
                checkedList,
                oldData,
                codeList,
                provinceList,
                cityList
            } = this.data;
            if (!oldData || oldData == 'ALL') {
                // 22.11.15
                codeList = []
                this.setData({
                    codeList
                })
                regionList.forEach(i => {
                    if (i.active) {
                        i.active = false
                        i?.children?.length && i.children.forEach(itm => {
                            if (itm) {
                                itm.active = false
                                itm?.children?.length && itm.children.forEach(is => is.active = false)
                            }
                        })
                    }
                    if (oldData == 'ALL' && i.code == 'ALL') {
                        i.active = true
                    } else {
                        i.active = false
                    }
                })
                provinceList = []
                cityList = []
                this.setData({
                    regionList,
                    provinceList,
                    cityList
                })
                return
            };
            let arrList = await this.getCityInfo(oldData)
            // 
            if (arrList.length > 0) {
                arrList.forEach(async item => {
                    // 先看缓存列表里面有不有-
                    if (!codeList.includes(item.code)) {
                        // 
                        await this.getRegion(item.code)
                        regionList = this.setBackFill(regionList, arrList);
                        checkedList = arrList;
                    } else {
                        regionList = this.setBackFill(regionList, arrList);
                        checkedList = arrList;
                    }
                })
            }
            this.setData({
                checkedList,
                regionList
            }, () => this.gsetCityAry())
        },
        setBackFill(list, data) { //填充数据             
            for (let region of list) {
                region.active = false;
                for (let item of data) {
                    if (region.code === item.code) {
                        if (region.isFather) return;
                        region.active = true;
                    }
                }
                if (region.children?.length) this.setBackFill(region.children, data)
            }
            return list;
        },

        //获取地区编码的层级关系
        getCityInfo(code) {
            let url = `${chainUrl}/search/transform/${code}`,
                that = this;
            let params = {
                url,
                method: 'GET'
            }
            return request(params).then(res => {
                let item = res[0],
                    districtData = [];
                districtData = that.formartDistrict(item);
                this.setData({
                    districtData
                })
                return districtData
            })
        },

        //格式化地区
        formartDistrict(data) {
            let arr = [];

            function flatten(obj) {
                let {
                    code,
                    name,
                    parent
                } = obj
                arr.push({
                    code,
                    name
                })
                if (parent) {
                    flatten(parent)
                }
            }
            flatten(data)
            arr = arr.reverse()
            return arr
        },


        // 将选中数据通过自定义事件传递出去
        submit() {
            let {
                regionList,
                checkedList
            } = this.data;
            this.setCheckedList(regionList, checkedList = []);
            this.triggerEvent('submit', checkedList) // 向外传递一个自定义事件
            this.close()
        },

        //设置选中项
        setCheckedList(regionList, checkedList) {
            for (let region of regionList) {
                if (region.active) checkedList.push(region);
                if (region.children?.length) this.setCheckedList(region.children, checkedList)
            }
        },

        // // 切换时更新活动状态
        changeRegion(e) {
          console.log('888999',this.data)
            this.getChildData(e.currentTarget.dataset);
        },

        // 设置当前数据状态，获取下一级数据
        getChildData(obj) {
            let {
                code,
                level,
                is_leaf,
                name
            } = obj; // active：当前点击节点的状态
            let sendRequest = true; // 是否发送请求获取数据
            let {
                isShowThird,
                regionList,
                codeList
            } = this.data;
            regionList = regionList.map(region => {
                if (level === '1') {
                    region.active = false;
                    if (region.code === code) region.active = true;
                } else{
                    // 第二级
                    region.children = region.children.map(city => {
                        city.active = false;
                        if (city.code === code)
                            if (city.isFather) {
                                city.active = false
                            } else {
                                city.active = true
                            };
                        if (city.children) {
                            city.children = city.children.map(item => {
                                item.active = false
                                return item
                            })
                        }
                        return city;
                    })
                    if (level === '3') {
                        sendRequest = false;
                    }
                }
                return region;
            })
            // 特殊判断 --香港这个
            if (!is_leaf && level == '1' && code != "ALL") {
                var obj;
                regionList.forEach(item => {
                    if (item.code == code) {
                        obj = item
                        item.active = true
                    }
                })
                this.triggerEvent('submit', [obj])
                this.close()
                this.setData({
                    regionList
                }, () => this.gsetCityAry());
                return
            }
            this.setData({
                regionList
            }, () => this.gsetCityAry());
            if (codeList.includes(code)) sendRequest = false;
            if (code === "ALL") {
                this.triggerEvent('submit', [this.data.regionList[0]])
                return
            }
            if (level === '3' || !is_leaf) {
                this.submit()
            }
            if (level === '2' && !isShowThird) {
              this.submit()
          }
            // 发送请求获取对应数据
            sendRequest && this.getRegion(code, name)
        },
        getArea(e) {
            const {
                code,
                level
            } = e.currentTarget.dataset
            let {
                regionList
            } = this.data
            if (level != 3) {
                throw ('数据错误');
                return
            }
            regionList = regionList.map(region => {
                region.children = region.children.map(city => {
                    if (city.children) {
                        city.children = city.children.map(area => {
                            area.active = false
                            if (area.code == code) {
                                if (area.isFather) {
                                    area.active = false
                                } else {
                                    area.active = true
                                }
                            }
                            return area
                        })
                    }
                    return city;
                })
                return region;
            })
            this.setData({
                regionList
            });
            this.submit()
        },

        // 递归添加子级
        addChild(list, code, data) {
            // 
            for (let region of list) {
                if (region.code === code) {
                    region.children = data.map(item => item);
                    break;
                } else {
                    let childList = region.children;
                    let len = region.children?.length;
                    if (len) this.addChild(childList, code, data);
                }
            }
            return list;
        },

        //获取地区
        async getRegion(code = '', name) {
            let {
                regionList,
                codeList
            } = this.data;
            if (!app.globalData.login) return;
            let data = await common.getSerDistrict({
                code
            });
            if (!code) { //省
                let text = this.data.headText
                data.unshift({
                    active: false,
                    code: "ALL",
                    level: "1",
                    name: text,
                    parent: "ALL",
                    is_leaf: true
                })
                regionList = data.map(province => { //增加状态和孩子节点
                    province.children = [];
                    return province;
                });
                // regionList[0].active = false; //默认设置第一个--选中
                this.setData({
                    regionList
                }, () => this.gsetCityAry());
                // 获取第二级
                // this.getRegion(regionList[0].code);
                this.getRegion(regionList[1].code, regionList[1].name);
            } else {
                if (name) {
                    data.unshift({
                        active: false,
                        code,
                        level: "3",
                        name: "全部" + name,
                        parent: "ALL",
                        is_leaf: false,
                        isFather: true,
                    })
                }
                codeList.push(code); // 存储已经获取过子级数据的父级节点code
                regionList = this.addChild(regionList, code, data);
                this.setData({
                    regionList,
                    codeList
                }, () => this.gsetCityAry());
            };
            wx.setStorageSync(this.data.storageObj.regionList, this.cacheRegion(JSON.parse(JSON.stringify(regionList))));
            wx.setStorageSync(this.data.storageObj.codeList, codeList);
        },

        // 缓存并初始化已经加载的地区
        cacheRegion(list) {
            for (let region of list) {
                if (region.children?.length) this.cacheRegion(region.children);
            }
            return list;
        },


        // 获取--设置 ：市的孩子数组 
        gsetCityAry() { //每一次设置数据源的时候掉一次
            const {
                regionList
            } = this.data
            // 
            let arr = regionList.filter(item => item.active)
            if (arr.length <= 0) {
                this.setData({
                    provinceList: []
                }, () => this.gsetAreaAry([]))
                return []
            }
            arr = arr[0]?.children || []
            setTimeout(() => {
                this.setData({
                    provinceList: arr
                }, () => this.gsetAreaAry(arr))
            }, 200)
            return arr
        },
        // 获取-设置 区的数组 
        gsetAreaAry(cityList) {
            if (cityList.length <= 0) {
                this.setData({
                    cityList: []
                })
                return
            }
            let arr = cityList.filter(item => item.active)
            if (arr.length <= 0) {
                this.setData({
                    cityList: []
                })
                return []
            }
            arr = arr[0]?.children || []
            setTimeout(() => this.setData({
                cityList: arr
            }), 200)
            return arr
        }
    }
})