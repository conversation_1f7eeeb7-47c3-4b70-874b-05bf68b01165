<!-- 融资时间 -->
<HalfScreenPop visible="{{visible}}" showFooter="{{false}}" position="{{position}}" bindsubmit="submit" bindclose="close" startDistance="{{startDistance}}" disableAnimation="{{true}}" _maskClosable="{{false}}" zIndex="{{10}}" footHeigh="110rpx" class="con" content-class="content-class">
  <view slot="customContent" class="list" style="margin-top:{{top}}px;">
    <block wx:for="{{orginList}}" wx:key="index">
      <view class="list-item {{item.checked&&'active'}}" catchtap="getTimeItem" data-item="{{item}}" data-index="{{index}}">
        <view class="text">
          {{item.name}}
        </view>
        <view class="icon">
        </view>
      </view>
    </block>
  </view>
</HalfScreenPop>