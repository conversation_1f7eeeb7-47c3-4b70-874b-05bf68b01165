Component({
  properties: {
    top: {
      type: Number,
      value: 0
    },
    visible: {
      type: <PERSON><PERSON><PERSON>,
      value: false,
      observer(val) {
        if (val) {
          this.init()
        }
      }
    },
    oldData: {
      type: String
    },
    dataList: {
      type: Array,
      value: [],
      observer(val) {
        if (val.length > 0) {
          this.setData({ 'orginList': val })
          return
        }
      }
    }
  },
  data: {
    orginList: []
  },
  lifetimes: {
    attached() {

    }
  },
  methods: {
    init() {
      let { orginList, oldData } = this.data
      if (oldData) {
        orginList.forEach(item => {
          item.checked = false
          if (item.code == oldData) {
            item.checked = true
          }
        })
      } else {
        orginList.forEach(item => {
          item.checked = false
          if (item.code == 'none') {
            item.checked = true
          }
        })
      }
      this.setData({ orginList })
    },
    getTimeItem(e) {
      let { item: { code }, index } = e.currentTarget.dataset
      let { orginList } = this.data
      orginList = orginList.map(i => {
        i.checked = false
        if (i.code != code) return i;
        i.checked = i?.checked ? false : true;
        return i
      })
      this.setData({ orginList }, () => this.submit())
    },
    submit() { //点击确定--传一个数组出去 回填的时候也是根据这个数组来 
      let { orginList } = this.data
      let arr = orginList.filter(item => item.checked)
      if (arr.length) {
        this.triggerEvent('submit', arr[0]) //单选只有一个
      }
    },
    close() {
      this.triggerEvent('close')
    }
  }
})
