<import src="/template/null/null"></import>
<import src="/template/more/more"></import>
<import src="/template/loading/index"></import>
<view class="finpages">
  <!-- 头部 -->
  <view class="head">
    <!-- input框 -->
    <view class="ipt-box">
      <view class="sear"></view>
      <input type="text" placeholder="请输入企业名称" placeholder-class="pla" class="ipt" confirm-type='search' value="{{bazaarParms.com_name}}"  bindinput="gosearch" data-item="{{bazaarParms.ent_name}}" />
      <view class="sear-r" bindtap="inputcancel"> 取消 </view>
      <!-- <view class="sear-r {{ isSearPop ? 'searActive': headFilterData.curId!=-1 ?'checkActive':''}}" catchtap="openSearPop">
        <view class="sear-r-text">招商优选</view>
        <view class="sear-r-ico"></view>
      </view> -->
    </view>
  </view>
  <!-- 头部筛选框 -->
  <view class="searPop" wx:if="{{isSearPop}}">
    <view class="content">
      <block wx:for="{{headFilterData.list}}" wx:key="index">
        <view class="{{(headFilterData.curId==item.id)&&'active'}}" catchtap="clickHeadList" data-item="{{item}}">
         {{item.name}} 
          <text wx:if="{{item.text}}" catchtouchstart="tooltip" data-item="{{item}}"></text>
        </view>
      </block>
    </view>
  </view>
  <!-- 筛选条件 筛选框 -->
  <block>
    <DropDownMenu
      height="{{filtrateHeight}}"
      dropDownMenuTitle="{{dropDownMenuTitle}}"
      class="drop-menu"
      bindsubmit="onFlitter"
      isFocus="{{isFocus}}"
    />
    <view class="sci_line"></view>
  </block>
  <block>
    <!-- 卡片!bazaarIsNull -->
    <view wx:if="{{!bazaarIsNull}}">
      <scroll-view bindrefresherrefresh="bazaarRefresher" refresher-triggered="{{bazaarIsTriggered}}" refresher-enabled bindscrolltolower="bazaarloadMore" scroll-y style="height: {{cardHeight}}px;" class="sci_scroll">
        <view class="bus-card">
          <block>
            <block wx:if="{{bazaarlist.length>0}}">
              <block wx:for="{{bazaarlist}}" wx:key="index">
                <!-- 卡片 -->
                <Card obj="{{item}}"  bindgoDetail="goDetail" />
              </block>
            </block>
            <view wx:else style="height: {{cardHeight}}px;">
              <template is="load"></template>
            </view>
          </block>
          <view wx:if="{{bazaarlist.length>=bazaarParms.page_size}}" style="width: 100%;">
            <template is='more' data="{{hasData:bazaarHasData}}"></template>
          </view>
        </view>
      </scroll-view>
    </view>
    <!--暂无数据 bazaarIsNull-->
    <view wx:if="{{bazaarIsNull}}" style="width: 100%;height: {{cardHeight}}px;">
      <template is='null'></template>
    </view>
  </block>
</view>