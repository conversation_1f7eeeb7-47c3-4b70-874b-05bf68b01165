<!--subPackage/pages/searchPark/detail/index.wxml-->
<wxs src="./timeFormat.wxs" module="tools" />
<view class="detail">
  <view  class='filterContainer'  catchtouchmove="true" wx:if="{{tools.stringToArray(parkObj.image,parkObj).length}}">
  <!-- 切换二维码内容 -->
  <swiper class="swiper"  current="" bindchange="changeswiper" >
      <swiper-item class="swiper-item" wx:for="{{tools.stringToArray(parkObj.image,parkObj)}}" wx:key='index'>
        <view > 
          <view class="erweicode_view">
            <image class="erweicode_qq" src="{{item}}"></image>
          </view>
        </view>
      </swiper-item>
   </swiper>
   <!-- 小圆点 -->
   <view class="bottom_text" >
     <view class="color_yuan {{activeIndex == index?'color_yuan1':''}}" wx:for="{{tools.stringToArray(parkObj.image,parkObj)}}" wx:key="index"></view>
   </view>
 
  </view>
  <view class="body">
    <view class="header">
      <view class="title">
        {{parkObj.parkName}}
      </view>
      <view class="tag">
        <view class="tag-item" wx:for="{{tools.stringToArray(parkObj.parkLabel) }}" wx:key="index">
          {{item}}
        </view>
      </view>
      <view class="parameter">
        <view class="parameter-item">
          <view>园区性质</view>
          <view style="color:#E72410;">{{filterItem.park_nature[parkObj.parkNature]}}</view>
        </view>
        <view class="parameter-item">
          <view>园区级别</view>
          <view>{{filterItem.park_level[parkObj.parkLevel]}}</view>
        </view>
        <view class="parameter-item">
          <view>建筑面积</view>
          <view style="color:#1E75DB;">{{parkObj.parkArea}}㎡</view>
        </view>
        <view class="parameter-item">
          <view>建筑时间</view>
          <view>{{tools.timeFormat(parkObj.constructionTime)}}</view>
        </view>
      </view>
      <view class="intro">
        <view class="industry">
          <view class='fnc'>主导产业：</view>
          <view><text wx:for="{{parkObj.leadingIndustry}}">{{filterItem.leading_industry_code[item.leadingIndustryCode]}}</text></view>
        </view>
        <view class="industry">
          <view class='fnc'>详细地址：</view>
          <view style="color: #1E75DB;" bindtap="openMap" data-item="{{parkObj.areaStr+parkObj.address}}"><image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>" style="width: 32rpx;height: 32rpx;padding-top: 5rpx;margin-right: 8rpx;"></image>{{parkObj.areaStr+parkObj.address}}</view>
        </view>
      </view>
    </view>
    <view class="profile">
      <view class="profile-title">
        园区概况
      </view>
      <view class='profile-item'>
        <view class="profile-item-title">
          园区简介
        </view>
        <view class="profile-item-nav">
          <rich-text nodes="{{parkObj.simpleIntroduce||'暂无数据'}}"/>
        </view>
      </view>
      <view class='profile-item'>
        <view class="profile-item-title">
          园区优势
        </view>
        <view class="profile-item-nav">
          <rich-text nodes="{{parkObj.parkAdvantage||'暂无数据'}}"/>
        </view>
      </view>
      <view class='profile-item'>
        <view class="profile-item-title">
          发展规划
        </view>
        <view class="profile-item-nav">
          <rich-text nodes="{{parkObj.developmentPlan||'暂无数据'}}"/>
        </view>
      </view>
    </view>
    <view class="profile">
      <view class="profile-title">
        优势政策
      </view>
      <view class='profile-item'>
        <view class="profile-item-nav">
          <rich-text nodes="{{parkObj.policyDetails||'暂无数据'}}"/>
        </view>
      </view>
    </view>
  </view>
  <view class="bottom">
    <view class="{{['button',entNum?'':'button-empty']}}" bindtap="jumpEntList">
      入园企业（{{entNum||0}}）
    </view>
  </view>
</view>
