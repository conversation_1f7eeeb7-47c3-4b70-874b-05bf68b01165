<!-- 下拉tag示的多选 -->
<HalfScreenPop showCloseBtn="{{false}}" visible="{{visible}}" position="{{position}}" bindsubmit="submit" bindclose="close" startDistance="{{startDistance}}" disableAnimation="{{true}}" _maskClosable="{{false}}" zIndex="{{10}}" footHeigh="110rpx" cancelBtnText="取消">
  <view slot="customContent" class="area" style="margin-top:{{top}}px;">
    <view class="region-wrap">
      <scroll-view scroll-y style="max-height: 720rpx">
        <view wx:for="{{list}}" wx:key="idx">
          <view class="title" wx:if="{{item.showTitle}}"> {{ item.title }}</view>
          <view class="list" wx:if="{{item.allList.length > 0}}">
            <view
              class="item {{items.status && 'active' || 'none'}}"
              wx:for="{{item.allList}}"
              wx:for-index="index" 
              wx:for-item="items"
              catchtap="checkItem"
              data-item="{{items}}"
              data-parent='{{item.id}}'
              data-single='{{item.singleSlect}}'
              data-slot='{{item.slot}}'
            >
              <text>{{items.text}}</text>
            </view>
          </view>
          <!-- 自定义筛选项目 -->
          <view class="customFilterContent">
            <slot name="{{ item.slot}}"></slot>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</HalfScreenPop>