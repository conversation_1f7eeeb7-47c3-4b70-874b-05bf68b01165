// subPackage/pages/financing/components/multipleChoice/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    top: {
      type: Number,
      value: 0
    },
    visible: {
      type: Boolean,
      value: false,
      observer(val) {
        if (val) {
          this.init()
        }
      }
    },
    oldData: {
      type: Array
    },
    dataList: {
      type: Array,
      value: [],
      observer(val) {
        if (val.length > 0) {
          this.setData({ 'orginList.allList': val })
          return
        }
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    orginList: { allList: [], leftList: [], rightList: [], centerList: [] }
  },
  lifetimes: {
    attached() {

    }
  },
  methods: {
    init() {
      let { orginList, oldData } = this.data
      if (oldData.length) { //説明有回填数据
        orginList.allList = orginList.allList.map(item => {
          item.status = ""
          oldData.forEach(i => {
            if (item.id == i) item.status = 'checked'
          })
          return item
        })
      } else {
        orginList.allList.forEach(item => item.status = '')
      }
      this.segmentation(orginList.allList, orginList, 'orginList')
    },
    // 过滤的方法 
    segmentation(orginList, obj, name) { //分割数组 
      let list = JSON.parse(JSON.stringify(orginList))
      obj.leftList = []
      obj.rightList = []
      obj.centerList = []
      list.forEach((i, idx) => {
        let num = idx % 3
        if (num == 0) {
          obj.leftList.push(i)
        } else if (num == 1) {
          obj.centerList.push(i)
        } else {
          obj.rightList.push(i)
        }
      })
      let len = list.length / 3;
      if (obj.centerList.length < len) { //为了适配样式
        obj.centerList.push({ text: '' })
      }
      this.setData({ [name]: obj })
    },
    // 选择item 
    checkItem(e) {
      let { item: { id } } = e.currentTarget.dataset
      let { orginList } = this.data
      if (id == 'all') { //说明点击到不限了 
        return
      }
      orginList.allList.map(item => {
        if (item.id != id) return item;
        if (item.status == 'checked') {
          item.status = ''
        } else {
          item.status = 'checked'
        }
        return item
      })
      this.segmentation(orginList.allList, orginList, 'orginList')
    },
    submit() { //点击确定--传一个数组出去 回填的时候也是根据这个数组来 
      let { orginList } = this.data
      let newList = orginList.allList.filter((item) => item.status == 'checked')
      let codeList = []
      if (newList.length > 0) {
        codeList = newList.map(item => item.id)
      }
      this.triggerEvent('submit', codeList)
    },
    close() {
      this.triggerEvent('close')
    }
  }
})
