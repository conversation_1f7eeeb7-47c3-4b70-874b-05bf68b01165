<HalfScreenPop showFooter="{{false}}" showCloseBtn="{{false}}" visible="{{visible}}" position="{{position}}" bindsubmit="submit" bindclose="close" startDistance="{{startDistance}}" disableAnimation="{{true}}" _maskClosable="{{false}}" zIndex="{{10}}" footHeigh="110rpx" cancelBtnText="取消">
  <view slot="customContent" class="area" style="margin-top:{{top}}px;">
  <view class="region-wrap" catch:touchmove="_disabledPenetrate" catchtap="_disabledPenetrate">
    <!-- 省 -->
    <scroll-view style="{{!isShowThird && 'width:50%;'}}" scroll-y="{{true}}" class="list province-wrap red">
      <view wx:for="{{regionList}}" wx:key="code" wx:for-item="province">
        <view class="item items {{province.active && 'actived'}}" data-code="{{province.code}}" data-level="{{province.level}}" catchtap="changeRegion" data-is_leaf="{{province.is_leaf}}" data-name="{{province.name}}">
          <text>{{province.name}}</text>
        </view>
      </view>
    </scroll-view>
    <!-- 市-->
    <scroll-view style="{{!isShowThird && 'width:50%;'}}" scroll-y="{{true}}" class="list city-wrap" wx:if="{{provinceList.length}}">
      <view wx:key="code" wx:for-item="city" wx:for="{{provinceList}}">
        <view class="item {{city.active && 'actived'}}" data-code="{{city.code}}" data-level="{{city.level}}" catchtap="changeRegion" data-is_leaf="{{city.is_leaf}}" data-name="{{city.name}}">
          <text>{{city.name}}</text>
        </view>
      </view>
    </scroll-view>
    <!-- 区  -->
    <block wx:if="{{isShowThird}}">
      <scroll-view scroll-y="{{true}}" class="area-wrap list" wx:if="{{cityList.length}}">
        <view wx:key="code" wx:for-item="area" wx:for="{{cityList}}">
          <view class="item {{area.active && 'actived'}}" data-code="{{area.code}}" data-level="{{area.level}}" data-parent="{{area.parent}}" catchtap="getArea">
            <text>{{area.name}}</text>
          </view>
        </view>
      </scroll-view>
    </block>
  </view>
  </view>
</HalfScreenPop>