import {
  home,
  common
} from '../../../service/api'
import {
  deteleObject
} from '../../../utils/util'
import {
  getHeight
} from '../../../utils/height';
import {
  handleSearchHight,
  tirms
} from '../../../utils/formate';
import {
  hasPrivile
} from '../../../utils/route'
import {
  collect
} from '../../../utils/mixin/collect'
import {
  getShareUrl,
  handleShareUrl
} from '../../../utils/mixin/pageShare'
const checkMixin = require('../../../utils/mixin/check');
const app = getApp();
Page({
  behaviors: [checkMixin()],
  onReady() {
    this.getHeight()
  },
  data: {
    showLoginPage: false,
    isLogin: true,
    vipVisible: false,
    recordY: 0,
    privileFlag: true,
    flag: true,
    dropDownMenuTitle: ['所属地区', '主导产业', '园区级别', '其他'],
    headFilterData: {
      curId: '-1',
      list: [{
          name: '不限',
          id: '-1'
        },
        {
          name: '科创版',
          id: '1'
        },
        {
          name: '一年融两轮',
          id: '2',
          text: '公司在12个月以内有两次及以上融资'
        },
        {
          name: '独角兽',
          id: '3',
          text: '估值超过10亿美元的公司'
        },
        {
          name: '千里马',
          id: '4',
          text: '估值在10亿人民币～10亿美金之间的公司'
        }
      ]
    },
    wordList: [],
    // 请求相关
    bazaarParms: {
      page_index: 1, //偏移量
      page_size: 10, //每页多少条
      parkName: ''
    },
    bazaarlist: [], //获取列表
    bazaarIsFlag: true, //节流 true允许
    bazaarHasData: true, //  是否还有数据
    bazaarIsNull: false, // list长度是否为0
    bazaarIsTriggered: false, // 下拉刷新状态
    count: 0, //数量
    isFocus: 0, //只是为了弹窗不共存
    // privileFlag: true,
  },
  touchEnd(e) {
    setTimeout(() => {
      let query = wx.createSelectorQuery();

      query.select('.sci_text').boundingClientRect(r => {
        if (r) {
          this.setData({
            regionTop: r.top
          })
        }
      }).exec();
    }, 300)
  },
  onLoad() {
    app.showLoading()
    handleShareUrl()
    const that = this
    setTimeout(function () {
      that.initGetList(() => wx.hideLoading())
    }, 1000)

  },
  handleLogout() {
    this.setData({
      isLogin: false
    })
  },
  onShow() {},
  /********这里目前用不到-改版了*********/
  async confirm(e) { //点击搜索时触发 
    let val = e.detail.value
    if (val.trim()) {
      home.financingHis({
        keyword: tirms(val)
      }) //历史记录
    }
    this.setData({
      'bazaarParms.parkName': val
    }, () => {
      this.bazaarRefresher()
    })
  },
  async getHisWord(list = []) { //历史次
    let {
      wordList,
      hotWord = []
    } = this.data
    let [err, res] = await app.to(home.financingGetHis())
    console.log(1111, err, res);
    if (err) return
    // 还要做个处理 就是 根据name找到一样的删除原来的 push新来的
    res = res.reverse().map(item => {
      let len = item.key_word.length
      let val = len > 5 ? item.key_word.slice(0, 5) + '...' : item.key_word
      return {
        name: item.key_word,
        word: val
      }
    })
    if (list.length) {
      wordList = [...list, ...res]
    } else {
      wordList = [...hotWord, ...res]
    }
    wordList = deteleObject(wordList)
    // console.log(wordList)
    this.setData({
      wordList
    })
  },
  clickWord(e) { //点击热词
    let {
      item
    } = e.currentTarget.dataset
    this.setData({
      'bazaarParms.ent_name': item.name
    }, () => {
      this.bazaarRefresher()
    })
  },
  focus() { //聚焦时 
    this.setData({
      isSearPop: false,
      isFocus: ++this.data.isFocus
    })
  },
  /*****************/
  goSearch() { //去搜索页面 
    const {
      bazaarParms
    } = this.data
    this.setData({
      isSearPop: false,
      isFocus: ++this.data.isFocus
    }, () => {
      // 跳转到搜索页面
      app.route(this, `../components/sear/index?ent_name=${bazaarParms['ent_name']}`)
    })
  },
  // 查园区输入
  inputChange(e) {
    this.setData({
      flag: false
    })
    this.setData({
      'bazaarParms.parkName': e.detail.value,
    }, () => {
      setTimeout(() => {
        this.setData({
          flag: true
        })
      }, 100)
      this.bazaarRefresher()
      // this.getHisWord()
    })
  },
  isScrollUp(e) {
    if (this.data.recordY > e.detail.scrollTop) {
      this.setData({
        privileFlag: true
      })
    }
    this.setData({
      recordY: e.detail.scrollTop
    })
  },
  vipButtonClick() {
    this.setData({
      vipVisible: true
    })
  },
  //获取列表
  initGetList(callback) {
    const that = this;
    let {
      bazaarlist,
      bazaarHasData,
      bazaarParms
    } = that.data;
    that.setData({
      bazaarIsNull: false,
      bazaarIsFlag: false,
      bazaarHasData: true
    });
    let obj = JSON.parse(JSON.stringify(bazaarParms));
    console.log(22222, obj)

    // if (obj.page_index > 5) {
    //   if(!this.data.isLogin){
    //     this.setData({showLoginPag:true})
    //   }else{
    //     hasPrivile({
    //       privileType: '查园区-园区列表'
    //     }).then(res => {
    //       this.setData({
    //         privileFlag: res
    //       })
    //     })
    //   }
    //   this.setData({
    //     bazaarHasData: true,
    //     bazaarIsFlag: true,
    //   })
    //   return
    // }
    obj.currentPage = bazaarParms.page_index
    obj.pageSize = bazaarParms.page_size
    delete obj.page_size
    delete obj.page_index
    home.parkList(obj).then(res => {
      let {
        data,
        total,
        needVip
      } = res
      console.log(res)
      let ary = []
      if (!data) {
        bazaarHasData = false;
        data = [];
      } else if (data.length < bazaarParms.page_size) {
        bazaarHasData = false;
      }
      if (obj.currentPage == 1) {
        that.setData({
          count: total
        })
      }
      ary = data
      that.setData({
        bazaarlist: bazaarlist.concat(ary),
        bazaarHasData,
        bazaarIsFlag: true,
      }, () => {
        if (!that.data.bazaarlist.length) that.setData({
          bazaarIsNull: true,
          bazaarHasData: true
        });
        needVip && this.vipButtonClick();
      })
      callback && callback()
    }).catch(err => {
      callback && callback()
      app.showToast('获取数据失败!请稍后再试')
      that.setData({
        bazaarlist: [],
        bazaarIsNull: true,
        bazaarHasData: true,
        count: 0
      });
    })
  },
  // 下拉刷新
  bazaarRefresher() {
    const that = this;
    wx.showNavigationBarLoading()
    let {
      bazaarParms,
      type
    } = that.data
    let obj = {
      ...bazaarParms,
      page_index: 1,
      page_size: 10
    }
    that.setData({
      bazaarParms: obj,
      bazaarlist: [],
      bazaarHasData: true,
    }, () => that.initGetList(() => {
      that.setData({
        bazaarIsTriggered: false
      })
      wx.hideNavigationBarLoading()
    }))
  },
  //加载更多
  bazaarloadMore() {
    console.log(333)
    let {
      bazaarParms,
      bazaarHasData,
      bazaarIsFlag
    } = this.data;
    if (!bazaarHasData) return;
    if (!bazaarIsFlag) return; //节流
    bazaarParms.page_index += 1;
    this.setData({
      bazaarParms
    }, () => this.initGetList());
  },
  // 筛选
  onFlitter(e) {
    let {
      dropDownMenuTitle,
      bazaarParms,
      bazaarlist
    } = this.data
    const obj = e.detail;
    this.setData({
      flag: false
    })
    bazaarParms = {
      ...bazaarParms,
      page_index: 1,
      page_size: 10,
      ...obj
    }
    if (!bazaarParms['invest_date']) { //特殊處理--解決覆蓋問題
      delete bazaarParms['invest_date']
    }
    bazaarlist = []
    this.setData({
      bazaarParms: bazaarParms,
      bazaarlist,
      dropDownMenuTitle
    }, () => {
      app.showLoading('加载中...')
      this.setData({
        bazaarlist: [],
        bazaarHasData: true
      })
      setTimeout(() => {
        this.setData({
          flag: true
        })
      }, 100)
      this.initGetList(() => wx.hideLoading())

    })
  },
  getHeight() {
    const that = this;
    getHeight(that, ['.sci_line', '.sci_text', '.drop-menu', '.head'], (data) => {
      let {
        res,
        screeHeight
      } = data
      let cardHeight = screeHeight - res[0]?.height - res[1]?.height - res[2]?.height - res[3]?.height
      let query = wx.createSelectorQuery();
      query.select('.input_box').boundingClientRect(r => {
        cardHeight += r.top
        that.setData({
          cardHeight
        })
      }).exec()


    })
  },
  // 头部相关筛选事件
  openSearPop() {
    let bool = this.data.isSearPop ? false : true
    // let isFocus = bool ? true : false //这里只是为了关闭下面得弹窗-不让弹窗共存
    this.setData({
      isSearPop: bool,
      isFocus: ++this.data.isFocus
    })
  },
  tooltip(e) {
    let {
      item: {
        text
      }
    } = e.currentTarget.dataset
    app.showToast(text, 'none', 1200)
  },

  clickHeadList(e) { //点击头部筛选列表
    let {
      bazaarParms
    } = this.data
    let {
      item: {
        id
      }
    } = e.currentTarget.dataset
    if (id != '-1') {
      bazaarParms['optimization'] = id
    } else {
      bazaarParms['optimization'] = ''
    }
    this.setData({
      'headFilterData.curId': id,
      isSearPop: false,
      bazaarParms
    }, () => {
      this.bazaarRefresher()
    })
  },
  async collect(e) { //点击收藏 
    let that = this
    const comDetail = e.detail
    // console.log(comDetail)
    if (!comDetail.ent_id) {
      app.showToast("该融资主体未关联企业暂不支持收藏")
      return
    }
    let params = {
      region: comDetail?.region || comDetail?.area_code,
      ent_name: comDetail.com_name.length ? comDetail.com_name.join('') : (comDetail.com_name || ''),
      logo: comDetail.logo
    }
    collect(that, comDetail, 'bazaarlist')
  },
  async goDetail(e) { //去详情页面
    if (e.currentTarget.dataset.item > 49) {
      await hasPrivile({
        privileType: '查园区-园区详情'
      }).then(res => {
        if (!res) {
          this.vipButtonClick()
          return
        } else {
          if (e.detail) {
            let url = `./detail/index?data=${encodeURIComponent(JSON.stringify(e.detail))}`
            app.route(this, url)
          }
        }
      })
    } else {
      if (e.detail) {
        let url = `/subPackage/pages/searchPark/detail/index?data=${encodeURIComponent(JSON.stringify(e.detail))}`
        app.route(this, url)
      }
    }
  },
  onShareAppMessage: function () {
    return {
      title: `邀请你查看全国${this.data.count}个产业园区信息`, //自定义转发标题
      path: getShareUrl(`/subPackage/pages/searchPark/index`), //分享页面路径
      imageUrl: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/aa5.png' //图片后面换
    };
  },
})