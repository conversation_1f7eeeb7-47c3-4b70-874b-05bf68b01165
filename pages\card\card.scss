@import "/template/null/null.scss";

/* tabs */
.card-page {
  background-color: #fff;
  height: 100vh;
  width: 750rpx;
  overflow: hidden;
}

.card-page .list_content {
  background-color: #f7f7f7;
}

.card-page .list_content .no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #20263A;
}

.card-page .list_content .no_data image {
  width: 406rpx;
  height: 240rpx;
  margin-top: 170rpx;
  margin-bottom: 40rpx;
}

.card-page .list_content .no_data .add_card_btn {
  width: 350rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #FFB2AA 0%,#E72410 100%);
  border-radius: 8rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  margin-top: 52rpx;

}


.select-box {
  margin-top: 16rpx;
  height: 96rpx;
  background-color: #fff;
  border-top: 1px solid #f5f5f5;
}

.card-list {
  color: #74798C;
  overflow-y: auto;
}

.card-list .card-list-item {
  padding: 32rpx 24rpx;
  display: flex;
  background-color: #fff;
}

.card-list .card-list-item {
  border-top: 20rpx solid #f7f7f7;
}

/* .card-list .card-list-item:first-of-type {
  margin-top: 0 !important;
} */



.card-list .card-list-item .logo {
  width: 124rpx;
  margin-right: 24rpx;
}

.card-list .card-list-item .logo image {
  width: 124rpx;
  height: 124rpx;
  border-radius: 4rpx;
}

.card-list .card-list-item .info-box {
  flex: 1;
  font-size: 24rpx;
}

.card-list-item .info-box .name {
  color: #20263A;
  font-size: 32rpx;
  display: flex;
  justify-content: space-between;
  /* border-bottom: 2px solid #fafafa; */
  min-height: 60rpx;
  position: relative;
}

.card-list-item .info-box .name::after {
  content: " ";
  width: 100%;
  height: 2rpx;
  background: #EEEEEE;
  position: absolute;
  bottom: 0;
  transform: scaleY(0.5);
}

.card-list-item .info-box .name .img {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

.card-list-item .info-box .name text {
  max-width: 400rpx;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  /* 这里是超出几行省略 */
  overflow: hidden;
}

.card-list-item .info-box .share {
  width: 40rpx;
  height: 40rpx;
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAAAAXNSR0IArs4c6QAAADZQTFRFAAAAYGCAb3qQanWKcHiHc3iMcXaJdHiNdHmLdHmNcniLc3iKc3iMc3mMc3iMc3mMc3mMdHmMZkPFGQAAABF0Uk5TAAgXGCAzNH+Pj5OXm9ff7/P+2BI/AAAAiElEQVQ4y+3USQ6AIAwF0OKEAwr//pc1OKYNFTau9O9KX8KiaYk+HOM89thYWpwJg2FwuDoCAo7BgDb1UT0Dnr0AiptFJwmjqwrg5igPd5eHh8vC0+Xg5WhZnuDtZDjUnYCj6gQ0k+aUEcY0TSEsmfUP34M+va5EHQKrHdT0/KT0Ic28M18+tStpXA4Oo/sabAAAAABJRU5ErkJggg==") no-repeat;
  background-size: contain;
  margin: 0 !important;
  padding: 0 !important;
}

.card-list-item .info-detail {
  display: flex;
  flex-wrap: wrap;
  margin: 20rpx 0;
}

.no_margin {
  margin: 0 !important;
}

.card-list-item .info-detail view {
  height: 32rpx;
  line-height: 32rpx;
  background-size: 32rpx;
  background-position: left center;
  padding-left: 40rpx;
}

.card-list-item .info-detail view:not(:last-child) {
  margin-right: 50rpx;
}

.card-list-item .info-detail .area {
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAWJJREFUWEftl89OwkAQh2ei4YIHMB58BB/Dg0BMNMrFAzWxd56HMyWhFy5gwBjogdfwFUw8cACuYxZTWW2bziy7bUzssdmd7+tv/2SKUPKDJfPhWyAIoykA3SSFaOZ7rVsTUU5NTWBBWRDfaxolFYT5NRMCOmxfACcmCQDQvZqXVjN+xxQww8ezDhZAwN2XSB8C2iUnEkiDFLMHhvMpICZPAdHMfzQ8BYyaid0dhNFYbR4V+5PXeJbGnjZ+EEZ3X8uBE99rtPUxVgX6/Zfz7Xaz7nYf1jqkEAEFx8rxEgA/NqvVtS7hXECDXwDQWwWPLjudq/c4BacCeXAl4UyAA3cmwIU7EZDArQtI4VYFTODWBEzh1gR6vdFJtVZ7BaCz3+c877q2dgyVRL1+WtUvmTy4tQQ4oKwxrAQ4DaRUglPzbzSlhbZk6V2xNPyf40U9Yelt+X9Tethqy2cb/fPJMdkzPgHuBbMwNWrV9AAAAABJRU5ErkJggg==") no-repeat;
  background-size: contain;
  margin-bottom: 20rpx;
}

.card-list-item .info-detail .seat_size {
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAU1JREFUWEftljFLw0AUx/8v1eLg2EEQIYUODv0GjhIyOFhHOdSAHfwY+SCVi2hmKyhiMzgWN4dsFhqpg4OjkxSf2Fo11IuYk1bhbn333vvll7vjETJWcNRaAaEBYDlrX0bs2rJ4b3vTvVLtIVXA9y9n7MpTD0QLOZu/pfFdctMu+77f/6qOEkDKc5uKhS6Y75NOe0lVQAUnpZyj4mIPQGkWBVuI1dt8AODEE245j4UgbHUB2AbgvxvI8/fTOZoGpg5gboExYAwYAxMyEIQXu2BUQTQPUGn4/nFN/x0c1DkFrD7AD2B+BCH2hLv/GnkfSIIwOv69ht9hU9MTzkYa4DBaI4srDFQB1MGIaTgPai9m1EGDug0CYn6mjrflnKUARl0OwmidwU3gg1KXYGSXQLUd4Zx8rjc2E/4hAN3vHs//oYGpA5gzYAwYA5Mx8ALceGMwj9HS7AAAAABJRU5ErkJggg==") no-repeat;
  background-size: contain;
}

.card-list-item .info-detail .region {
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABYFJREFUWEelV11sFFUU/s7stqD0oRpEKA+USEyjJkDECBEikXZ3aWosEbHs7HanURMMPmDUUKLGNWDA6ANGiBBJOkt3lkZKagImu7NFMGKKEZOaYPCnBgxS8K0kNlna3TnmzuyuM9PZ7hbv48y55373nO9851zCHJaqZR4HpE6ANwJoAdAIwA/mWyBcBqSLPl9hMNoV+qlWt1SLoZrUnwKwDwRxcPXFOC/5uLd7e/D7asazAujr65tP85r2g2mX6YgxAUISBqXJkEavXv32b/F5+SMbHuQ7xipIHAIjAjIjI9YhnrrxVk9PT64SkIoAiocPgUk4zRHxgXvqpIPbtrXdnu1WYp9U37SbmXpBmA/iNN8Z31IJhCeAeDzub3543WnrcP4LhE5FDv5YLZz2/xZfMAhQswBx7beRZ+PxeN7twxNAQsu+x+C4OLyO/OtledOfczm8ZKtpZ5dNc/4iiBYT84FYJLinKoD+gfTKQkG6CGC+RNjYHQ58495kOS50gMxKENz4pV6SBsPhVpMT9nU8pT9tMIbNb758i9LV/of9/4wIqFpmCKBOMI4okcCrdmOTF/VNewGTlH7XWTmAD/HU+LvufKua/jmAlwFDVeRQT0UAR4+eXjivYd51MFAvSc32Gzl4AeQBHiSQiBQY3Aqm1hLp3Pk2I4bCmGk7NflAT8+WiRIIRwQSqUyMmVQwp5VIcLMdaULTdzNwQIiOZKCzu9tZ4ybpGGfMfAO9MTnwoZOU2dMAdwAcUeSg5glA1fRPAbxGwK6YHPikZGSGvm7pTVHflXghbNWkvgmEYaEXi+7PL25vb79T8qEm9TdA+NidWkcEVC2TBagVBnUo0bavbJstx8AlRQ48MVtFqJr+A4A1YLQqkcDZku1xLRs0wGl3dF0AipvBa+x1n0gNx5gNVVxSkQMOErnBqEm9H4QIMSuxSDBRvoSpC3QJwKgiB1Z7pyCpj4Cw1h3mRHL4RSZjAMyDSiT4wqwRSOonQdjqzrXZTwgXAFxQ5MCGSgAs9IQdsXDgaBl96utHwfnLIrc8fWNJJVm1ynTpdQALfUahJRrd/KstAi8BdMwdRWcKkvpOEA553VQtRgfEB5Vw8HWvKCSSmf1M1AvmUSUSLIfZJKimnwDQ5b6cA0C5Xhl5np5cYq/X4ycyTxoGiRAKATpST1K8pBN9fUONVL9gP4AdonFJEkJ2BS3+vynUtQ6+Zru0eymhWQkEjsfk4PuOWk5ln4fBSVNwLDEaA0wXK6zBBDlIFFHCbacc+8olOFNfZgAoavd5K9+Ty+1REE77+9MrCxLFzU5pARErL8oLEvcq4dDPTvkeaqS6e68IgZJAoW65LVNRim11f86cfpgPKhHvfB8+/EXDgvsWLhN7FjXmxuyi41RA/TMrNTNvL+w827FqsX5UGEgSr69ltPIiZZk3jLyPjbXR6MxZseJEpGqZjwB6E8xjk7dvr965c9s/s9W/+1+xc14RA4kXnzx1wJk7s6ZHAKwCeECRg9vnAkBN6SfB2CpKkqfH181pJCtzYeDsQygURCoaCEZvTA45OlwlQLbGk4Pf95jStckxhFQlod0goWWfY/CXJtMl6lS2/9ekPPNuNZ0zZlm6mpqXfY3vguzbIN4HIFfsct95Hm6NX2khOGB+R4kEP6iWtpoAFKXUGqsYEz72r41GnynrvPjfl8q0kEGimYk3wTFFDrxS7fCKZei1MR4/529eMTUEog4xFfm4bmMJhHU4zgmxAfOZa2MjW7xG8LtOQWmjNRk1CRDivXDLxxwqSCQk2BzFhNjwdOVHyP8GYIbaDgIoaUPD3Rw+pxTM1IimE+b4LhbzIE+PR2d7A1biQ80kdDsQnFi+YmovE/zXfh/ZU2vO3X7+BSYqoT+iUNe7AAAAAElFTkSuQmCC") no-repeat;
  background-size: contain;
}

.add_btn {
  position: fixed;
  right: 32rpx;
  bottom: 80rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(134deg, #4AB8FF 0%, #E72410 100%);
  box-shadow: 0px 4rpx 4rpx 2rpx rgba(32, 38, 58, 0.2);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add_btn>image {
  width: 40rpx;
  height: 40rpx;
}

/* 我的名片 */
.m_card {
  width: 100%;
  padding: 20rpx 24rpx 40rpx;
  background: #F7F7F7;
}

.m_card_i {
  background: #fff;
  padding: 40rpx;
  border-radius: 8rpx;
  position: relative;
  box-shadow: 0px 4px 18px 0px rgba(221,221,221,0.5000);
}

.my_card_btn {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.my_card_btn button {
  width: 340rpx !important;
  height: 80rpx;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  opacity: 1;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  font-size: 32rpx;
  margin: 0 !important;
}

.my_card_btn button:first-of-type {
  background: linear-gradient(90deg, #F56E60 0%, #E72410 100%);
}

.my_card_btn button:last-of-type {
  /* background: #E72410; */
  background: #fff;
  color: #E72410;
  border: 2rpx solid rgba(231, 36, 16, 0.50);
}


.head_name {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  align-items: center;
}

.head_name text:first-of-type {
  font-size: 30rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  line-height: 35rpx;
}

.head_name text:last-of-type {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #1E75DB;
  line-height: 40rpx;
  margin-left: 16rpx;
}

.com_name {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 28rpx;
  margin-top: 24rpx;
  margin-bottom: 50rpx;
}

.img_text {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.img_text image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.img_text text {
  flex: 1;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
}

.code_img {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  top: 40rpx;
  right: 40rpx;
}

.edit_img {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  bottom: 40rpx;
  right: 40rpx;
}

.no_mine_card {
  width: 100%;
  height: 500rpx;
  padding: 20rpx 26rpx 0 26rpx;
  margin-bottom: 40rpx;
}
.null_tip {
  width: 702rpx;
  height: 360rpx;
  background-color: #fff;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.null_tip image {
  width: 230rpx;
  height: 230rpx;
  margin-top: -40rpx;
}

.null_tip text {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  margin-top: 24rpx;
}

.no_mine_card_btn {
  width: 100%;
  height: 80rpx;
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}
.no_mine_card_btn .btn{
  width: 340rpx;
  height: 80rpx;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 74rpx;
  text-align: center;
}
.no_mine_card_btn view:first-of-type {
  background: linear-gradient(90deg, #F56E60 0%, #E72410 100%);
  color: #FFFFFF;
}

.no_mine_card_btn view:last-of-type {
  border: 2rpx solid rgba(231, 36, 16, 0.50);
  color: #E72410;
  background-color: #fff;
}

.card_entry_wrap {
  width: 100%;
  height: 480rpx;
  /* background: #FFFFFF; */
}
.card_entry {
  padding: 0 22rpx;
  display: flex;
  justify-content: space-between;
}
.card_box {
  width: 340rpx;
  height: 336rpx;
  background: #FFFFFF;
  box-shadow: 0px 4px 18px 0px rgba(221,221,221,0.5000);
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #fff;
  display: flex;
  flex-direction: column;
}
.entry_top {
  width: 100%;
  height: 172rpx;
  position: relative;
}
.entry_bg {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

.entry_bt {
  color: #9B9EAC;
  padding: 20rpx;
  position: relative;
  flex: 1;
}
.entry_bt .entry_name {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}
.entry_bt .entry_name text {
  color: #20263A;
  font-weight: 600;
}

.entry_name .entry_icon {
  width: 36rpx;
  height: 36rpx;
}
.star_title {
  /* padding-top: 40rpx; */
  padding-left: 24rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  line-height: 38rpx;
  position: relative;
  margin-bottom: 20rpx;
}

/* .star_title::before {
  content: " ";
  width: 6rpx;
  height: 28rpx;
  background: #E72410;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  position: absolute;
  top: 46rpx;
  left: 22rpx;
} */

.has_star_card_box {
  padding: 0 24rpx;
}

.star_card_box.active {
  max-height: 420rpx !important;
}

.star_card_box {
  width: 702rpx;
  margin: 20rpx auto;
  max-height: 216rpx;
  background: #fff;
  border-radius: 8rrpx 8rpx 8rpx 8rpx;
  opacity: 1;
  padding: 36rpx;
  position: relative;
  overflow: hidden;
  transition: .3s;
  /* transition: cubic-bezier(0, 2.41, 1, -0.66); */
}
.space {
  height: 40rpx;
  transition: .3s;
}
.space.active {
  height: 0;
}
.star_card_box .show_more {
  width: 32rpx;
  height: 32rpx;
  position: absolute;
  right: 36rpx;
  bottom: 32rpx;
  transition: .3s;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAPlJREFUWEftlDEOgkAQRf9ESLyDhRQ23sLEeAIbIw1H4SbYYGw8gTGx8wC2FHgOMWvAaABddnYtsBjKXYb/+H9mCD0/1LM+BEAcEAfEgf92IE2P4xvdN74aRGE4v9psTW6t1oEkSYbkjy4gTACV+/BmXIhKHMUJoABKZXl2nsZxXHz7gc4INtvDEkrtAHhciIY4UIBoFa0Xe517xh6wgbAVL6GMAOVLHAgXcTaACcJV3ApAB1GevxuOkXm7F1gR1IvacTzvKICDuLUDL5AmRHVq7HbnKdAV1iBgGrWuBWYdwUccALrm3LQ9fwIwfZxzLwDigDggDvTuwAMehJIhm+SBJAAAAABJRU5ErkJggg==') no-repeat center center;
  background-size: 100% 100%;
}

.star_card_box .show_more.active {
  /* transform: rotateZ(180deg); */
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAOBJREFUWEftlDEOgjAUhv+n3XEzbphg4uIhOIOLqydzdfEMHMNBh27GTTYGENMaYlCk5XVgeayk/b5+bR5h5I9G5kMEpIAUkAJBBe6raGsG2fySn7gDjS1g4M8aRwOeEHZcCZbAB07qffK65EoMFmjBa2jLJ8RciUECP/AKqRWYIuNKeAt0wRf6YQvc4lnMlfAS6IM3r58r4RTwgYdI9AoMgXMl/gpkgFon0RmgBOa1V0ibO3cNna/ruBZlvllqFF3regvYjRQOKLH3hbdKeKx1vgHXaUP/i4AUkAJSQAq8ADJwjSFnmI7WAAAAAElFTkSuQmCC') no-repeat center center;
  background-size: 100% 100%;
}

.card_head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  margin-bottom: 20rpx;
}
.card_head_l {
  display: flex;
  align-items: center;
}
.card_head_l text:first-of-type {
  font-size: 30rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  line-height: 35rpx;
}

.card_head_l text:last-of-type {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #1E75DB;
  line-height: 40rpx;
  margin-left: 16rpx;
}

.card_head_r {
  display: flex;
  align-content: center;
}

.card_head_r text:first-of-type {
  width: 36rpx;
  height: 36rpx;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAAA65JREFUWEfNl0+IE3cUx78viYvoFvdQqNTCWmgPotBLD6WXLugmu+qC0mzpZqKZgRYr66EHpZctTamC0kIFXfA2EzcTFqu04Irmz6KXshRaeumhh5TNwVoFDyvV1k6y88pvMhvyZ8bMZBLxd53ffN/nvXm/9/0N4QVb9ILxYOBAqvr9CA0NqwiHTsof7v2jWwEGCqTry6NVXi+B8AbAlSEKv5NI7HvwLKiBAV3OFd4zGVcBvNwAYNzh6p+TiqI8dYMaCFAmW0oxzEsgbAZDBF8CIW5BMLJyMnrkuQFpevErgOfs4GsA4nIyuqxl89+BqA5FNCcnxs84QfWtQqqqbqZNry40gjKXa8yTHx2ZKIvA9ec7boIwZkPF5cT4tXaovgBlrizv4Or6DwDergfgEhv/TCvKYVGhxlLVG9tpKLIC0E7xKUNhHjs6E/upeU9gIC13azeYlqwg1jI1Nv467ta4ml78GuCTdW4uV8oru9LpdG0DKhCQtlA8gBAvAhgGUCNgLiVFzzn1Rjqdjux8891vAZywYe4DJPrrx75UKKMXPmPgNICIdZLCFJdnxm84wczPXxneum2baOoJu9l/4+r6lKJMVgL3UEem4LsADslS7BcnGHs4imO/x67MrSePHk3Pzn7wOPAps2xg0xaR6T5b7Ocq1w5/nNwvoDqWNRxNXgTRdhvmfKW8cqq5Z3quUKsNWB25xMa9adfmXSi+D+KsNRyBGhifysnovBO47x5qz5SYz66WVz53yzSjF79gcNoO9DgEih+VxvPdYMTzrqes3QYI/EkqGcs4iXcMR3CFDUwqSux3LzBdgdxswEk8lyu9YpjmYmMSW0ZanVGUA/e9wrgCdbOB9gCdw5EX2binPMvV3SA7PpmTDfxHxsyxxNRDx5OkF2MmWFwzxHAEgdMpKfaln6q4NnX9drflV882kC3MgnC+MRxDZlJOTHQYph+4lgqpCzfHKBS+XZ8ZNCcnna8IXm3AD8jG3hagTK5wjBmXxMMna2svOU1TPzYQGEjTCxfq5scVWYq93i7o1waCA2Xz10F0UExhWYpNNQv2YgPBgfT8ar2huUQIXdwQZObXAHzj1wYCAVm9MTLydxcRXzbQD6DVlt+WFkX/NhAISLwsjvPo6FvWgGtehhGJ1Gr/PnW7w/QS2POk7qd4L1pd3b4X0SDvNIAyuXwqiJDbuyaTp6QVKaoJjcZmTS/wIIC8aspS1GIZFJDv5GQpGmoB8prJoPd5+r6DhmjW/x+DJ8o0QMMnywAAAABJRU5ErkJggg==");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.card_head_r text:last-of-type {
  margin-left: 32rpx;
  width: 36rpx;
  height: 36rpx;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAAAlJJREFUWEftWD9o1GAU/z0vp4KCCm4W7CocqKOoIBQu0klBOpjUJFDpQSdnbxERHF0KFiuXaFKk7p4XrYsWOggqdXBwqKCTyw0WpLnck9z1JJ75nyvckG8L773f+32/93353vcRxmxQWj5Pn7057bpOhQhCVCwzOh0Xn+ZuiJ/T5EhMyLLWTjrs6iBcSpMAzC/LJNQkaepbkrhEhIzVtRPsdN4BNJkE9H8f3nLYvXhTnv4eF5+IkG7az0G4BqBDQL27s72kaVfbUeCLi6uHDx05Og/CfaBXXl2VqlpuQrvAP0E4COYHqizeigP123WrdRegujeZ7Xb72MLCzK+o+FiFlp+0KkKJNj0Q3umc07TpjTSEHhvNCyWh9LYfj7OaVv2Yi1DDss8Q8CEp4HCytPF/FdItm9PMfNS+qlTtcRlfQobZUvyzZiK9/81bYIrcUcnV4uMgmugpwaz64xRZNP5RaBh0UEIvcOCcPHGwp7HSUpj7Ex2UaNgzdJcVhHalyqSQbtmN/vLCsipX1wPKHWjfs5LFlTPMXhAalK5QqFhD3lqIUqFQqFDI96vPenT0mrmwTqD4D43dLht0lw7z+tzs5a/Dp32Yfc8O16ydY15CjnfjpC5qymx1KSsJf5yxYs8z42G2Fta0N0GoAHhP5dIVZWbqRx5Sj8wXE2UIzT4mf1El8VQQXsS2fy0BXTMPibBYItSU68GqR16ldfPVbYDrvXv9KAbDAfiOKov3QsnG5Wk0mpO0f9954ugHKj+OGwTq4vcBQdiIeyeKfWyIIzxq+x8ZaW9DIMQ+iQAAAABJRU5ErkJggg==");
  background-size: 100% 100%;
  background-repeat: no-repeat;

}

.star_com_name {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  /* line-height: 28rpx; */
  line-height: 36rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  background-color: #fff;
}

.active .star_com_name {
  line-height: 28rpx;
  display: block;
}

.no_star_card_box {
  margin: 16rpx auto;
  width: 702rpx;
  height: 360rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

}

.no_star_card_box image {
  width: 230rpx;
  height: 230rpx;
  margin-top: -50rpx;
}

.no_star_card_box text {
  display: block;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  line-height: 33rpx;
  margin-top: 24rpx;
  text-align: center;
}
.photo_btn_wrap {
  width: 100%;
  height: 168rpx;
  background-color: #fff;
  position: fixed;
  bottom: 0;
}
.photo_btn {
  width: 702rpx;
  height: 80rpx;
  border-radius: 8rpx;
  text-align: center;
  line-height: 80rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #fff;
  margin: 10rpx auto;
  background: linear-gradient(90deg, #F56E60 0%, #E72410 100%);
}

/* 分享弹窗 */
.share_mask {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  opacity: 1;
  z-index: 1;
}

.share_box {
  width: 702rpx;
  /* height: 480rpx; */
  background: #FFFFFF;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  opacity: 1;
  margin-top: 50%;
  margin-left: 50%;
  transform: translateX(-50%);
}

.my_card_btn_share {
  display: flex;
  /* margin-top: 40rpx; */
}

.my_card_btn_share button {
  width: 300rpx !important;
  height: 80rpx;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  opacity: 1;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  font-size: 32rpx;
}

.my_card_btn_share button:first-of-type {
  background: #FFFFFF;
  color: #E72410;
  border: 2rpx solid #E72410;
}

.my_card_btn_share button:last-of-type {
  background: #E72410;
}

/* 登录缺省页面 */
.card-login {
  position: relative;
  display: flex;
  flex-direction: column;
  /* justify-content: center; */
  align-items: center;
  width: 100%;
  height: 100vh;
  background: #FFFFFF;
}

.card-login::after {
  content: " ";
  width: 100%;
  height: 1px;
  background: #eee;
  /* background: red; */
  position: absolute;
  top: 0;
  left: 0;
  transform: scaleY(0.5);
}

.card-login::before {
  content: " ";
  width: 100%;
  height: 1px;
  background: #eee;
  /* background: red; */
  position: absolute;
  bottom: 0;
  left: 0;
  transform: scaleY(0.5);
}

.card-login image {
  width: 226rpx;
  height: 176rpx;
  margin-top: 310rpx;
}

.card-login .txt {
  padding-top: 60rpx;
  padding-bottom: 48rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 33rpx;
}

.card-login .btn {
  margin: 0;
  padding: 0;
  width: 350rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: #E72410;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #FFFFFF;
  text-align: center;
}
.weui-navbar .weui-navbar__slider::after {
  width: 68rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #E72410 0%, #F17B6F 100%);
  border-radius: 0;
  bottom: 0rpx;
}