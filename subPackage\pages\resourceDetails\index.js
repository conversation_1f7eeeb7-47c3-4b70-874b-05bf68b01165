// subPackage/pages/resourceDetails/resourceDetails.js
import { indResource } from '../../../service/api';
let WxParse = require('../../template/wxParse/wxParse.js');
const app = getApp();
const requestMap = {
  INNOVATION_CARRIER: 'getCarrierDetail',
  INDUSTRY_EXPERTS: 'getExpertDetail',
  INNOVATION_TRANSFORMATION: 'getTransformDetail',
};

Page({

  /**
   * 页面的初始数据
   */
  data: {
    type: 'INNOVATION_CARRIER',
    id: '',
    info: {},
    tabList: [
      { label: "交通" },
      { label: "教育" },
      { label: "医疗" },
      { label: "生活" },
      { label: "休闲" },
    ],
    fullAddress: "",
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad({ type, id }) {
    this.setData({
      type,
      id
    }, () => {
      this.getData();
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },
  getData() {
    app.showLoading('加载中');
    const { type, id } = this.data;
    indResource[requestMap[type]](id).then(res => {
      console.log('res', res);
      const { detail } = res;
      this.setRichText(detail);
      this.setData({
        info: res,
        fullAddress: `${res.detail.bearings_addr}`
      })
    }).finally(res => {
      wx.hideLoading()
    })
  },
  // 处理返回的富文本数据
  setRichText(detail) {
    detail.introduction && WxParse.wxParse('introduction', 'html', detail.introduction, this, 5)
    detail.direction && WxParse.wxParse('direction', 'html', detail.direction, this, 5)
    detail.territory && WxParse.wxParse('territory', 'html', detail.territory, this, 5)
  },
  // 去详情页面 
  toDetail({ currentTarget: { id } }) {
    const { type } = this.data;
    app.route(this, `/subPackage/pages/resourceDetails/index?type=${type}&id=${id}`);
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
})