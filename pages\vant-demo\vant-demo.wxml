<!--pages/vant-demo/vant-demo.wxml-->
<view class="vant-demo">
  <!-- 页面标题 -->
  <view class="demo-section">
    <view class="demo-title">Vant Weapp 组件演示</view>
  </view>

  <!-- 搜索框 -->
  <view class="demo-section">
    <view class="demo-subtitle">搜索框</view>
    <van-search
      value="{{ searchValue }}"
      placeholder="请输入搜索关键词"
      bind:change="onSearchChange"
      bind:search="onSearchConfirm"
      bind:clear="onSearchClear"
    />
  </view>

  <!-- 按钮 -->
  <view class="demo-section">
    <view class="demo-subtitle">按钮</view>
    <view class="button-group">
      <van-button type="primary" bind:click="onButtonClick">主要按钮</van-button>
      <van-button type="success" bind:click="onButtonLoading">成功按钮</van-button>
      <van-button type="warning" bind:click="showDialog">警告按钮</van-button>
      <van-button type="danger" bind:click="showPopup">危险按钮</van-button>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="demo-section">
    <view class="demo-subtitle">标签页</view>
    <van-tabs active="{{ activeTab }}" bind:change="onTabChange">
      <van-tab title="标签 1">
        <view class="tab-content">标签页 1 的内容</view>
      </van-tab>
      <van-tab title="标签 2">
        <view class="tab-content">标签页 2 的内容</view>
      </van-tab>
      <van-tab title="标签 3">
        <view class="tab-content">标签页 3 的内容</view>
      </van-tab>
    </van-tabs>
  </view>

  <!-- 单元格 -->
  <view class="demo-section">
    <view class="demo-subtitle">单元格</view>
    <van-cell-group>
      <van-cell title="单元格" value="内容" bind:click="onCellClick" is-link />
      <van-cell title="单元格" value="内容" label="描述信息" border="{{ false }}" />
    </van-cell-group>
  </view>

  <!-- 输入框 -->
  <view class="demo-section">
    <view class="demo-subtitle">输入框</view>
    <van-cell-group>
      <van-field
        value="{{ fieldValue }}"
        placeholder="请输入用户名"
        label="用户名"
        bind:change="onFieldChange"
      />
      <van-field
        value=""
        type="password"
        placeholder="请输入密码"
        label="密码"
      />
    </van-cell-group>
  </view>

  <!-- 下拉菜单 -->
  <view class="demo-section">
    <view class="demo-subtitle">下拉菜单</view>
    <van-dropdown-menu>
      <van-dropdown-item 
        value="{{ dropdownValue }}" 
        options="{{ dropdownOptions }}"
        bind:change="onDropdownChange"
      />
    </van-dropdown-menu>
  </view>

  <!-- 选择器按钮 -->
  <view class="demo-section">
    <view class="demo-subtitle">选择器</view>
    <van-cell title="选择城市" value="{{ pickerValue || '请选择' }}" bind:click="showPicker" is-link />
  </view>

  <!-- 弹出层 -->
  <van-popup show="{{ popupShow }}" bind:close="closePopup" position="bottom">
    <view class="popup-content">
      <view class="popup-title">弹出层内容</view>
      <view class="popup-text">这是一个从底部弹出的弹出层</view>
      <van-button type="primary" bind:click="closePopup" custom-style="margin: 20px;">关闭</van-button>
    </view>
  </van-popup>

  <!-- 选择器 -->
  <van-popup show="{{ pickerShow }}" bind:close="onPickerCancel" position="bottom">
    <van-picker
      columns="{{ pickerColumns }}"
      bind:confirm="onPickerConfirm"
      bind:cancel="onPickerCancel"
    />
  </van-popup>

  <!-- Toast 和 Dialog 组件 -->
  <van-toast id="van-toast" />
  <van-dialog id="van-dialog" />
</view>
