import { filterItem } from './filterItem';

module.exports = Behavior({
  data: {
    dropDownMenuTitle: ['所属地区', '主导产业', '园区级别', '更多筛选'],
    leadingIndustryCode: {
      id: 'leadingIndustryCode',
      title: '主导产业',
      cur: [], //选中的数据
      list: [], // 数据列表
      showTitle: false, // 是否显示标题
      singleSlect: true, // 是否为单选操作
      slot: '', // 自定义配置筛选项 插槽名称
    },
    parkLevel: {
      id: 'parkLevel',
      title: '园区级别',
      cur: [], //选中的数据
      list: [],
      showTitle: false,
      singleSlect: true,
      slot: '', // 自定义配置筛选项 插槽名称
    },
    policyCheckListCode: {
      id: 'policyCheckListCode',
      title: '园区政策',
      cur: [], //选中的数据
      list: [],
      showTitle: true,
      singleSlect: true,
      slot: 'policyCheckListCode', // 自定义配置筛选项 插槽名称
    },
    parkNature: {
      id: 'parkNature',
      title: '园区性质',
      cur: [], //选中的数据
      list: [],
      showTitle: true,
      singleSlect: false,
      slot: '', // 自定义配置筛选项 插槽名称
    },
    serviceListCode: {
      id: 'serviceListCode',
      title: '园区服务',
      cur: [], //选中的数据
      list: [],
      showTitle: true,
      singleSlect: true,
      slot: '', // 自定义配置筛选项 插槽名称
    },
    none: { code: 0, id: 'all', text: '不限', status: 'checked' },
    showDatePicker: false, // 控制时间选择
  },
  lifetimes: {
    ready() {},
    created(){ },
  },
  methods: {
    // 融资事件-获取筛选项(预加载)
    async _getFilterItems(){
      const this_ = this;
      const res = filterItem;
      const { leading_industry_code, policy_check_list_code, park_nature,park_level,service_list_code } = res;
      const arr= ['不限'];
      this.setData({ 
        'leadingIndustryCode.list':  this_.transformData(arr.concat(leading_industry_code)),
        'parkNature.list':  this_.transformData(arr.concat(park_nature)),
        'parkLevel.list':  this_.transformData(arr.concat(park_level)),
        'policyCheckListCode.list':  this_.transformData(arr.concat(policy_check_list_code)),
        'serviceListCode.list': this_.transformData(arr.concat(service_list_code)),
      });
      return res
    },
    //  获取近十年年份
    getYear(){
      let date = new Date(); 
      let startYear = date.getFullYear();//起始年份 
      let yearList = [];
      for (let i=0;i<=10;i++) {
        const y = startYear- i
        yearList.push(y)
      }
      return yearList
    },
    // 格式化筛选项数据
    transformData(data) {
      return data.map((item, index) => {
        return {code: index, id: item === '不限' ? 'all' : item, text: item};
      });
    },
  },
})