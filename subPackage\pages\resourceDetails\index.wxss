/* subPackage/pages/resourceDetails/resourceDetails.wxss */
@import '/subPackage/template/wxParse/wxParse.wxss';
@import './template/head/head.wxss';
.ellipsis {
  -webkit-line-clamp: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.resource-detail {
  height: 100vh;
  background-color: #F2F2F2;
}
.resource-detail .par {
  padding: 40rpx 32rpx;
  font-size: 28rpx;
  color: #20263A;
}
.resource-detail .list {
  padding: 0 24rpx;
  background-color: #fff;
}
.resource-detail .list .item {
  display: flex;
  padding: 28rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
}
.resource-detail .list .item .img {
  width: 100rpx;
  height: 100rpx;
  flex-shrink: 0;
}
.resource-detail .list .item .desc {
  margin-left: 20rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #3D4255;
  font-size: 24rpx;

}
.list .item .desc .title {
  font-size: 28rpx;
  color: #20263A;
  margin-bottom: 20rpx;
}
.list .item .desc .label {
  color: #9B9EAC;
}