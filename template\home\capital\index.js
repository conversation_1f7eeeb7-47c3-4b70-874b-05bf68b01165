import {
  hijack
} from '../../../utils/route';
import {
  formatterParams
} from '../../../utils/util';
const app = getApp()
module.exports = Behavior({
  data: {
    capitalOne: false, //1
    capitalTwo: false, // 2
    capitalThree: false, // 3
    capitalFour: false, //4
    // 判断是否选项框有值--高亮---这三个值外面也要用到
    capitalOne_val: false,
    capitalTwo_val: false,
    capitalThree_val: false,
    capitalFour_val: false,
    // 
    capitalShownavidx: -1,
    capitalDropDownMenuTitle: ['基金层级'],
    capitalRegionData: [], //选中的地区
    capitalOneData: {
      code: 'capital_area_name',
      curVal: '',
      children: [{
          code: '国家级',
          name: '国家级'
        },
        {
          code: '重庆',
          name: '重庆'
        },
        {
          code: '成都',
          name: '成都'
        },
        {
          code: '广州',
          name: '广州'
        },
        {
          code: '合肥',
          name: '合肥'
        },
        {
          code: '上海',
          name: '上海'
        },
      ]
    },
  },
  methods: {
    // 获取数据 
    async capitalPopData() {
      // res1 政策类型弹窗  ----res2 发文机关弹窗 
      // const [res1, res2] = await Promise.all([home.getcapitalType()])
      // if (res2?.items.length) {
      //   let arr = res2.items.map(i => {
      //     return {
      //       code: i.id,
      //       name: i.name
      //     }
      //   })
      //   this.setData({
      //     'capitalOneData.children': arr
      //   })
      // }
    },
    capitalTimeChoose(e) {
      let {
        item,
        source,
        shows
      } = e.currentTarget.dataset
      let soucreData = this.data[source],
        show_val;
      soucreData.children.forEach(i => {
        i.show = false
        if (i.code == item.code) {
          i.show = !item.show
        }
      })
      if (item.show) {
        soucreData.curVal = ''
        show_val = false
      } else {
        soucreData.curVal = item.code
        show_val = true
      }
      this.setData({
        [source]: soucreData,
        [shows]: show_val
      })
      this.capitalCloseHyFilter()
      this.capitalBackAll()
    },
    capitalBackAll() {
      // 处理数据 发请求 scope_code 
      const {
        capitalOneData,
      } = this.data
      let str = '',
        obj = {};
      if (capitalOneData.curVal) {
        obj[capitalOneData.code] = capitalOneData.curVal
      } else {
        delete obj[capitalOneData.code]
      }
      str = formatterParams(obj, {}, true)
      // console.log(obj)
      // console.log(str)
      this.onFlitter('capital', str)
    },





    /**关闭筛选*/
    capitalCloseHyFilter: function (e) {
      if (e && e.target.dataset['type'] && e.target.dataset['type'] == 'child') return;
      if (this.data.capitalOne) {
        this.setData({
          capitalOne: false,
          capitalTwo: false,
          capitalThree: false,
          capitalFour: false,
          capitalShownavidx: -1
        })
      } else if (this.data.capitalTwo) {
        this.setData({
          capitalTwo: false,
          capitalOne: false,
          capitalThree: false,
          capitalFour: false,
          capitalShownavidx: -1
        })
      } else if (this.data.capitalThree) {
        this.setData({
          capitalTwo: false,
          capitalOne: false,
          capitalThree: false,
          capitalFour: false,
          capitalShownavidx: -1
        })
      } else if (this.data.capitalFour) {
        this.setData({
          capitalTwo: false,
          capitalOne: false,
          capitalThree: false,
          capitalFour: false,
          capitalShownavidx: -1
        })
      }
    },
    capitalRapDistrictNav: hijack(function (e) {
      if (this.data.capitalShownavidx != -1) {
        this.capitalClickNav(this.data.capitalShownavidx)
        this.setData({
          capitalShownavidx: -1
        })
        this.capitalCloseHyFilter()
        return
      }
      if (this.data.capitalOne) {
        this.setData({
          capitalOne: false,
          capitalTwo: false,
          capitalThree: false,
          capitalFour: false,
          capitalShownavidx: 0
        })
      } else {
        this.setData({
          capitalOne: true,
          capitalTwo: false,
          capitalThree: false,
          capitalFour: false,
          capitalShownavidx: e.currentTarget.dataset.nav
        })
      }
    }, {
      type: 'searchs-btn',
      app: app
    }),
    capitalRapSourceNav: hijack(function (e) {
      if (this.data.capitalShownavidx != -1) {
        this.capitalClickNav(this.data.capitalShownavidx)
        this.setData({
          capitalShownavidx: -1
        })
        this.capitalCloseHyFilter()
        return
      }
      if (this.data.capitalTwo) {
        this.setData({
          capitalTwo: false,
          style_open: false,
          capitalOne: false,
          capitalThree: false,
          capitalFour: false,
          capitalShownavidx: 0
        })
      } else {
        this.setData({
          capitalTwo: true,
          style_open: false,
          capitalOne: false,
          capitalThree: false,
          capitalFour: false,
          capitalShownavidx: e.currentTarget.dataset.nav
        })
      }
    }, {
      type: 'searchs-btn',
      app: app
    }),
    capitalTapFilterNav: hijack(function (e) {
      if (this.data.capitalShownavidx != -1) {
        this.capitalClickNav(this.data.capitalShownavidx)
        this.setData({
          capitalShownavidx: -1
        })
        this.capitalCloseHyFilter()
        return
      }
      if (this.data.capitalThree) {
        this.setData({
          capitalTwo: false,
          capitalOne: false,
          capitalThree: false,
          capitalFour: false,
          capitalShownavidx: 0
        })
      } else {
        this.setData({
          capitalTwo: false,
          capitalOne: false,
          capitalThree: true,
          capitalFour: false,
          capitalShownavidx: e.currentTarget.dataset.nav
        })
      }
    }, {
      type: 'searchs-btn',
      app: app
    }),
    capitalFourFilterNav: hijack(function (e) {
      if (this.data.capitalShownavidx != -1) {
        this.capitalClickNav(this.data.capitalShownavidx)
        this.setData({
          capitalShownavidx: -1
        })
        this.capitalCloseHyFilter()
        return
      }
      if (this.data.capitalFour) {
        this.setData({
          capitalTwo: false,
          capitalOne: false,
          capitalThree: false,
          capitalFour: false,
          capitalShownavidx: 0
        })
      } else {
        this.setData({
          capitalTwo: false,
          capitalOne: false,
          capitalThree: false,
          capitalFour: true,
          capitalShownavidx: e.currentTarget.dataset.nav
        })
      }
    }, {
      type: 'searchs-btn',
      app: app
    }),
    //点击Nav切换发送请求
    capitalClickNav(index) {
      // switch (index) {
      //   case '1':
      //     this.opportunitySure()
      //     break;
      //   case '2':

      //     break;
      //   case '3':
      //     this.canyeSure()
      //     break;

      //   default:
      //     break;
      // }
    },
  },
})