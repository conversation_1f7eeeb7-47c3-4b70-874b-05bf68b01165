# Tailwind CSS 工具类使用指南

## 概述

本项目已集成了完整的 Tailwind CSS 工具类系统，所有类名都添加了 `h-` 前缀以避免与现有样式冲突。

## 文件结构

```
styles/
├── tailwind.scss       // Tailwind CSS 工具类（带 h- 前缀）
├── variables.scss      // 全局变量
└── mixins.scss        // 全局混合

pages/
└── tailwind-demo/     // 使用演示页面
    ├── tailwind-demo.js
    ├── tailwind-demo.wxml
    ├── tailwind-demo.scss
    └── tailwind-demo.json
```

## 使用方法

### 1. 引入 Tailwind CSS

在页面的 SCSS 文件中引入：

```scss
@import '../../styles/tailwind.scss';
```

### 2. 在 WXML 中使用工具类

```xml
<view class="h-flex h-justify-center h-items-center h-bg-blue-500 h-text-white h-p-4 h-rounded">
  <text>居中的蓝色卡片</text>
</view>
```

## 主要工具类分类

### 🎨 布局 (Layout)

#### Display
- `h-block` - display: block
- `h-inline-block` - display: inline-block
- `h-flex` - display: flex
- `h-grid` - display: grid
- `h-hidden` - display: none

#### Position
- `h-static` - position: static
- `h-relative` - position: relative
- `h-absolute` - position: absolute
- `h-fixed` - position: fixed
- `h-sticky` - position: sticky

#### Top/Right/Bottom/Left
- `h-top-0` - top: 0
- `h-right-4` - right: 8rpx
- `h-bottom-2` - bottom: 4rpx
- `h-left-0` - left: 0

### 📐 Flexbox

#### Flex Direction
- `h-flex-row` - flex-direction: row
- `h-flex-col` - flex-direction: column
- `h-flex-row-reverse` - flex-direction: row-reverse

#### Justify Content
- `h-justify-start` - justify-content: flex-start
- `h-justify-center` - justify-content: center
- `h-justify-between` - justify-content: space-between
- `h-justify-around` - justify-content: space-around

#### Align Items
- `h-items-start` - align-items: flex-start
- `h-items-center` - align-items: center
- `h-items-end` - align-items: flex-end
- `h-items-stretch` - align-items: stretch

### 📏 间距 (Spacing)

#### Padding
- `h-p-0` - padding: 0
- `h-p-2` - padding: 4rpx
- `h-p-4` - padding: 8rpx
- `h-p-6` - padding: 12rpx
- `h-px-4` - padding-left/right: 8rpx
- `h-py-2` - padding-top/bottom: 4rpx

#### Margin
- `h-m-0` - margin: 0
- `h-m-2` - margin: 4rpx
- `h-m-4` - margin: 8rpx
- `h-mx-auto` - margin-left/right: auto
- `h-my-4` - margin-top/bottom: 8rpx

### 📐 尺寸 (Sizing)

#### Width
- `h-w-full` - width: 100%
- `h-w-1/2` - width: 50%
- `h-w-20` - width: 40rpx
- `h-w-auto` - width: auto

#### Height
- `h-h-full` - height: 100%
- `h-h-screen` - height: 100vh
- `h-h-20` - height: 40rpx
- `h-h-auto` - height: auto

### 🔤 字体 (Typography)

#### Font Size
- `h-text-xs` - font-size: 20rpx
- `h-text-sm` - font-size: 24rpx
- `h-text-base` - font-size: 28rpx
- `h-text-lg` - font-size: 32rpx
- `h-text-xl` - font-size: 36rpx
- `h-text-2xl` - font-size: 40rpx

#### Font Weight
- `h-font-light` - font-weight: 300
- `h-font-normal` - font-weight: 400
- `h-font-medium` - font-weight: 500
- `h-font-semibold` - font-weight: 600
- `h-font-bold` - font-weight: 700

#### Text Align
- `h-text-left` - text-align: left
- `h-text-center` - text-align: center
- `h-text-right` - text-align: right

### 🎨 颜色 (Colors)

#### Text Colors
- `h-text-white` - color: #ffffff
- `h-text-black` - color: #000000
- `h-text-gray-600` - color: #969799
- `h-text-red-500` - color: #E72410
- `h-text-blue-500` - color: #1989fa

#### Background Colors
- `h-bg-white` - background-color: #ffffff
- `h-bg-gray-100` - background-color: #f7f8fa
- `h-bg-red-500` - background-color: #E72410
- `h-bg-blue-500` - background-color: #1989fa

### 🔲 边框 (Border)

#### Border Width
- `h-border` - border-width: 1rpx
- `h-border-2` - border-width: 2rpx
- `h-border-0` - border-width: 0

#### Border Color
- `h-border-gray-300` - border-color: #ebedf0
- `h-border-red-500` - border-color: #E72410

#### Border Radius
- `h-rounded` - border-radius: 8rpx
- `h-rounded-lg` - border-radius: 16rpx
- `h-rounded-full` - border-radius: 9999rpx

### 🌟 阴影 (Shadow)

- `h-shadow-sm` - 小阴影
- `h-shadow` - 中等阴影
- `h-shadow-md` - 大阴影
- `h-shadow-lg` - 超大阴影

### 🔄 动画 (Animation)

#### Transition
- `h-transition` - 基础过渡
- `h-transition-all` - 所有属性过渡
- `h-duration-200` - 过渡时长 200ms
- `h-ease-in-out` - 缓动函数

#### Transform
- `h-scale-105` - transform: scale(1.05)
- `h-rotate-45` - transform: rotate(45deg)
- `h-translate-x-2` - transform: translateX(4rpx)

### 📱 网格 (Grid)

#### Grid Template
- `h-grid-cols-2` - 2列网格
- `h-grid-cols-3` - 3列网格
- `h-gap-4` - 网格间距 8rpx

#### Column/Row Span
- `h-col-span-2` - 跨越2列
- `h-row-span-3` - 跨越3行

## 实际使用示例

### 卡片组件

```xml
<view class="h-bg-white h-rounded-lg h-shadow h-p-6 h-mb-4">
  <text class="h-text-lg h-font-semibold h-text-gray-800 h-mb-2 h-block">
    卡片标题
  </text>
  <text class="h-text-sm h-text-gray-600">
    卡片内容描述
  </text>
</view>
```

### 按钮组件

```xml
<view class="h-bg-blue-500 h-text-white h-py-3 h-px-6 h-rounded h-text-center h-transition h-duration-200">
  点击按钮
</view>
```

### 居中布局

```xml
<view class="h-flex h-justify-center h-items-center h-h-screen">
  <text>页面居中内容</text>
</view>
```

### 网格布局

```xml
<view class="h-grid h-grid-cols-2 h-gap-4">
  <view class="h-bg-gray-100 h-p-4 h-rounded">项目1</view>
  <view class="h-bg-gray-100 h-p-4 h-rounded">项目2</view>
</view>
```

## 响应式设计

虽然微信小程序主要针对移动端，但仍提供了响应式工具类：

```xml
<view class="h-w-full h-md:w-1/2 h-lg:w-1/3">
  响应式宽度
</view>
```

## 自定义扩展

如需添加更多工具类，可以在 `styles/tailwind.scss` 中扩展：

```scss
// 自定义工具类
.h-text-primary {
  color: #E72410;
}

.h-bg-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
```

## 最佳实践

1. **组合使用**：多个工具类组合使用，避免写自定义CSS
2. **语义化**：使用有意义的类名组合
3. **一致性**：在整个项目中保持一致的间距和颜色使用
4. **性能**：只引入需要的工具类

## 注意事项

1. 所有类名都有 `h-` 前缀，避免与现有样式冲突
2. 尺寸单位使用 `rpx`，适配微信小程序
3. 颜色系统与项目设计规范保持一致
4. 响应式功能在小程序中有限，主要用于展示语法

## 演示页面

访问 `pages/tailwind-demo/tailwind-demo` 查看完整的使用演示。
