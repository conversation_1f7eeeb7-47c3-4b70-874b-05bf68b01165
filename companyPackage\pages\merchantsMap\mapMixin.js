const app = getApp();
// 引入高德地图微信小程序SDK
var amapFile = require('../../lib/amap-wx130.js');
import {
  map
} from '../../../service/api';

module.exports = function (params) {
  return Behavior({
    data: {
      markers: [], //实际渲染的markers
      // 圈内markers集合
      insideMarkes: [],
      // 圈外markers
      outMarkes: [],
      // 如果当前是5k米 就显示一共有好多家
      fixMarkes: [],
      circles: [],
      mapCtx: null,
      // 防抖相关
      _debounceTimer: null,
      _isUpdating: false,

      // 中心点管理
      originalCenter: {
        latitude: '',
        longitude: '',
        locationName: '当前位置',
        adcode: ''
      }, // 原始中心点（功能：用户点击回到当前位置）
      currentCenter: {
        latitude: '',
        longitude: '',
        locationName: '',
        adcode: ''
      }, // 当前中心点（拖动或缩放后的中心点）--请求以及渲染

      // 业务回调函数
      _onCenterChange: null, // 中心点变化回调
      _onDataRefresh: null // 数据刷新回调
    },

    lifetimes: {
      attached() {},

      ready() {
        if (!this.mapCtx) {
          this.mapCtx = wx.createMapContext('map');
        }
      },

      detached() {
        this._cleanup();
      }
    },

    methods: {
      /**
       * 初始化地图
       */
      _initializeMap() {
        if (!this.mapCtx) {
          this.mapCtx = wx.createMapContext('map');
        }
        if (!this.myAmapFun) {
          // 初始化高德地图SDK
          this.myAmapFun = new amapFile.AMapWX({
            key: 'ea478181d97e3d92be0150c05603ddb6'
          });
        }
      },

      // 初始化定位服务
      _initializeLocation() {
        // 本地有就用本地
        const mapSearchItem = wx.getStorageSync('mapSearchItem');
        if (mapSearchItem) {
          const item = JSON.parse(mapSearchItem);
          this.updateCurrentCenter(
            item.location.lat,
            item.location.lng,
            item.title,
            this.getCityCode(item.adcode)
          );
          wx.removeStorageSync('mapSearchItem');
          return;
        }
        // 直接获取当前位置，更可靠
        this.getCurrentLocation((latitude, longitude, name, adcode) => {
          // 本地没有就用传进来的经纬度
          this.updateCurrentCenter(latitude, longitude, name, adcode);
        });
      },
      // 获取当前位置
      getCurrentLocation(callback) {
        const that = this;
        this.myAmapFun.getRegeo({
          success: function (data) {
            // 区域编码
            const adcode = data[0].regeocodeData.addressComponent.adcode
            const latitude = data[0]?.latitude;
            const longitude = data[0]?.longitude;
            const name = data[0]?.name;
            // 设置原始中心点
            that.setOriginalCenter(latitude, longitude, name, that.getCityCode(adcode));
            // 将经纬度抛出去 用于请求
            callback && callback(latitude, longitude, name, that.getCityCode(adcode));
          },
          fail: function (info) {
            console.log('初始获取地理位置失败', info);
            const {
              originalCenter
            } = that.data;
            if (originalCenter.latitude) {
              callback &&
                callback(
                  originalCenter.latitude,
                  originalCenter.longitude,
                  originalCenter.name,
                  originalCenter.adcode
                );
              return;
            }
            // 如果获取位置失败，使用默认位置（北京）
            callback &&
              callback(
                39.9042,
                116.4074,
                '北京市东城区东华门街道北京市人民政府'
              );
          }
        });
        // wx.getLocation 这个小程序不能一致掉
      },
      // 更新当前中心点
      updateCurrentCenter(latitude, longitude, name, adcode) {
        if (!latitude || !longitude || !name) return;

        // 将地图移动到当前中心点
        this.setMapCenter(latitude, longitude);
        // 更新圆圈
        this.updateCircle(latitude, longitude);
        // 首先设置当前中心点以及名称
        const currentCenter = {
          latitude,
          longitude,
          locationName: name,
          adcode
        };
        // 设置相关参数
        this.setData({
            currentCenter,
            'params.page_index': 1,
            'params.grids': [{
              lon: longitude,
              lat: latitude
            }],
            hasData: true,
            requestData: []
          },
          () => {
            // 请求列表
            this.getHot();
            this.getList();
          }
        );
      },
      /**
       * 清理资源
       */
      _cleanup() {
        // 清除防抖定时器
        if (this._debounceTimer) {
          clearTimeout(this._debounceTimer);
          this._debounceTimer = null;
        }
      },
      // 处理marks数据 
      mergeSameLocationMarkers(markers) {
        const mergedMap = new Map();

        for (const marker of markers) {
          const key = `${marker.latitude},${marker.longitude}`;

          if (!mergedMap.has(key)) {
            // 初始化
            mergedMap.set(key, {
              ...marker,
              count: 1,
              calloutContents: [marker.callout.content]
            });
          } else {
            const existing = mergedMap.get(key);
            existing.count += 1;
            existing.id = marker.id; // 用最后一个的 id
            existing.calloutContents.push(marker.callout.content);
            existing.callout.content = `${marker.callout.content}（等${existing.count}家）`;
            existing.label.content = existing.count;
          }
        }
        // 清理合并信息（删除 calloutContents）
        const result = Array.from(mergedMap.values()).map(marker => {
          const {
            calloutContents,
            count,
            ...rest
          } = marker;
          return rest;
        }).map(i => {
          return {
            ...i,
            label: {
              ...i.label,
              content: i.label.content + ''
            }
          }
        });
        return result;
      },
      // 获取区县markes坐标参数 
      async getOutMarkes() {
        // 获取当前区域编码 
        const {
          currentCenter: {
            adcode
          },
          params,
          exitAdcode,
          outMarkes
        } = this.data
        // 如果当前的adcode和存储的一样就不请求了 
        if (exitAdcode === adcode && outMarkes) return outMarkes;
        const res = await map.mapHots({
          level: 3,
          area_code_list: [adcode],
        })
        this.setData({
          exitAdcode: adcode
        })
        return res.filter(i => i.location).map(item => {
          const {
            location,
            region_id,
            region_name,
            total
          } = item
          const [longitude, latitude] = location.split(',');
          return {
            id: region_id,
            width: 0,
            height: 0,
            latitude,
            longitude,
            alpha: 0,
            label: {
              content: `${region_name}(等${total}家)`, // Use name if available, otherwise default
              color: '#fff',
              fontSize: 13,
              display: 'ALWAYS',
              borderRadius: 4,
              padding: 6,
              bgColor: '#F03F2D',
              textAlign: 'center',
              anchorY: '-40'
            }
          }
        })
      },
      // 获取数据生成marks
      async getHot() {
        // 逻辑 1.中心经纬度==后端(确定区县)=》获取两份数据： 一份是圈内数据 ，一份是圈外数据（市下---》区县名称以及经纬度）
        // 2.将数据保存起来 判断当前缩放程度决定渲染那套markers
        // 监听缩放，当缩放到一定程度展示不停markers 大于区县就圈外数据，小于区县就圈内数据
        const {
          params,
          currentCenter,
          count,
          scale,
          requestData,
          temScale
        } = this.data
        const centerLat = currentCenter.latitude;
        const centerLng = currentCenter.longitude;
        // const radius = params.radius; // 圆圈半径（米）
        // 圈内mark
        const tempMarkes = requestData.filter(i => i.location?.lat && i.location?.lon).map(i => {
          return {
            latitude: i.location.lat,
            longitude: i.location.lon,
            id: i.ent_id,
            iconPath: '../../image/m_ico.png', // 替换为你的图标路径
            width: 18,
            height: 24,
            callout: {
              content: `${i.ent_name}`, // Use name if available, otherwise default
              color: '#fff',
              fontSize: 13,
              display: 'ALWAYS',
              borderRadius: 4,
              padding: 6,
              bgColor: '#F03F2D',
              textAlign: 'center',
              anchorY: '-3'
            },
            label: {
              content: 1,
              color: '#fff',
              fontSize: 12,
              bgColor: 'transparent',
              anchorX: '0',
              anchorY: '-26',
              textAlign: 'center',
              width: 18,
              height: 24
            }
          }
        })
        const insideMarkes = this.mergeSameLocationMarkers(tempMarkes)
        // 圈外mark
        const outMarkes = await this.getOutMarkes()
        console.log('11111111', outMarkes);
        // 5km默认的时候 
        const fixMarkes = [{
          id: 0,
          width: 0,
          height: 0,
          latitude: +centerLat,
          longitude: +centerLng,
          alpha: 0,
          label: {
            content: `附近${params.radius/1000}km找到${count}家相关企业`,
            color: '#fff',
            fontSize: 13,
            display: 'ALWAYS',
            borderRadius: 4,
            padding: 6,
            bgColor: '#F03F2D',
            textAlign: 'center',
            anchorY: '-80'
          }
        }]
        // 为了解决地图放大缩小值回弹 用的临时缩放
        const temScales = temScale ? temScale : scale
        this.setData({
          'circles[0].radius': temScales < 9.7 ? 0 : params.radius,
          markers: temScales < 9.7 ? outMarkes : temScales === 9.7 ? fixMarkes : insideMarkes,
          insideMarkes: insideMarkes,
          outMarkes: outMarkes,
          fixMarkes: fixMarkes
        });
        // 这里是请求marck
        // this.setData({
        //   count: res.count,
        //   'markers[0].label.content': num,
        //   'markers[0].label.anchorX': isAn ? anchorX : 0
        // });
      },
      /**
       * 防抖函数 - 防止频繁调用，只在停止触发后执行
       * @param {Function} func 要执行的函数
       * @param {number} delay 延迟时间（毫秒）
       */
      _debounce(func, delay = 1000) {
        // 如果正在更新中，直接返回
        if (this._isUpdating) {
          return;
        }

        // 清除之前的定时器
        if (this._debounceTimer) {
          clearTimeout(this._debounceTimer);
        }

        // 设置新的定时器
        this._debounceTimer = setTimeout(() => {
          console.log('防抖执行，延迟时间:', delay);
          this._isUpdating = true;

          try {
            func();
          } catch (error) {
            console.error('防抖函数执行出错:', error);
          } finally {
            this._isUpdating = false;
            this._debounceTimer = null;
          }
        }, delay);
      },
      // 将区县转市 区域编码
      getCityCode(code) {
        const str = String(code).padStart(6, '0');

        // 特殊直辖市映射（写死）
        const special = {
          '110000': '110100',
          '120000': '120100',
          '310000': '310100',
          '500000': '500000'
        };

        if (special[str]) return special[str];

        // 如果是重庆的区县（5001XX），特殊处理为 500000
        if (str.startsWith('5001')) return '500000';

        // 如果是地市级（以00结尾但非0000）
        if (str.endsWith('00') && !str.endsWith('0000')) return str;

        // 普通县级码 → 返回其归属市级
        return str.slice(0, 4) + '00';
      },

      //  经纬度 -> 地址名称（逆向解析）- 高德地图SDK
      getLocationName(longitude, latitude, callback) {
        const that = this
        // 参数验证
        if (!longitude || !latitude || typeof callback !== 'function') {
          console.error('getLocationName: 参数无效');
          callback && callback('');
          return;
        }
        // 使用高德地图SDK进行逆向地理编码
        this.myAmapFun.getRegeo({
          location: `${longitude},${latitude}`, // 高德地图格式：经度,纬度
          success: data => {
            const adcode = data[0].regeocodeData.addressComponent.adcode
            // 获取格式化地址
            let address = '';
            // 高德SDK返回的数据结构可能是数组或对象
            let regeocode = null;
            if (Array.isArray(data) && data.length > 0) {
              regeocode = data[0].regeocodeData;
            } else if (data && data.regeocodeData) {
              regeocode = data.regeocodeData;
            } else if (data && data.regeocode) {
              regeocode = data.regeocode;
            }

            if (regeocode) {
              if (regeocode.formatted_address) {
                address = regeocode.formatted_address;
              } else if (regeocode.addressComponent) {
                // 手动拼接地址
                const addr = regeocode.addressComponent;
                address = `${addr.province || ''}${addr.city || ''}${
                  addr.district || ''
                }${addr.township || ''}`;
              }
            }
            console.log('解析出的地址:', address);
            callback(address || '未知位置', that.getCityCode(adcode));
          },
          fail: error => {
            console.error('高德逆地理编码失败:', error);
            callback('未知位置');
          }
        })
      },
      // 地图区域变化事件处理（拖动地图时触发）
      regionchange(e) {
        const that = this;
        // 拖拽
        if (e.type === 'end' && e.causedBy === 'drag') {
          // console.log('regionchange', e);
          // 中心坐标
          const {
            centerLocation
          } = e.detail;
          this._debounce(() => {
            if (!this.mapCtx) {
              console.error('mapCtx 未初始化');
              return;
            }
            that.getLocationName(
              centerLocation.longitude,
              centerLocation.latitude,
              (locationName, adcode) => {
                that.updateCurrentCenter(
                  centerLocation.latitude,
                  centerLocation.longitude,
                  locationName,
                  adcode
                );
              }
            );
          }, 800); // 800ms防抖，拖动停止后执行
        }
        // 放大缩小-真机上中心是不变的 markers要跟着变化
        if (e.type === 'end' && e.causedBy === 'scale') {
          this._debounce(() => {
            const {
              insideMarkes,
              outMarkes
            } = this.data;
            // 拿到缩放比例
            const temScale = e.detail.scale;
            //小于等于9.7 markers用圈外否者markers用圈内
            this.setData({
              temScale: temScale,
              'circles[0].radius': temScale <= 9.7 ? 0 : this.data.params.radius,
              markers: temScale <= 9.7 ? outMarkes : insideMarkes
            });
          }, 800);
        }
      },

      /**
       * 手动设置地图中心
       * @param {number} latitude 纬度
       * @param {number} longitude 经度
       */
      setMapCenter(latitude, longitude) {
        const {
          currentCenter
        } = this.data;
        if (
          latitude == currentCenter.latitude &&
          longitude == currentCenter.longitude
        )
          return;
        if (!this.mapCtx) {
          console.error('mapCtx 未初始化');
          return;
        }

        if (!latitude || !longitude) {
          console.error('setMapCenter: 经纬度参数无效');
          return;
        }

        this.mapCtx.moveToLocation({
          latitude,
          longitude,
          success: () => {
            this.setData({
              latitude,
              longitude
            });
          },
          fail: err => {
            console.error('设置地图中心失败:', err);
          }
        });
      },

      //   设置数据刷新回调函数
      setDataRefreshCallback(callback) {
        if (typeof callback === 'function') {
          this._onDataRefresh = callback;
        } else {
          console.error('setDataRefreshCallback: callback必须是函数');
        }
      },
      // 设置原始中心点（用户当前位置）
      setOriginalCenter(latitude, longitude, locationName = '当前位置', adcode) {
        const originalCenter = {
          latitude,
          longitude,
          locationName,
          adcode
        };
        this.setData({
          originalCenter
        });
      },

      /**
       * 回到原始中心点
       */
      backToOriginalCenter() {
        const that = this;
        that.getCurrentLocation((latitude, longitude, name, adcode) => {
          that.updateCurrentCenter(latitude, longitude, name, adcode);
        });
      },
      /**更新圆圈和*/
      updateCircle(latitude, longitude) {
        this.setData({
          circles: [{
            latitude: +latitude,
            longitude: +longitude,
            color: 'transparent',
            fillColor: 'rgba(231,36,16,0.14)',
            radius: this.data.params.radius,
            strokeWidth: 1.8
          }]
        });
      },
    }
  });
};