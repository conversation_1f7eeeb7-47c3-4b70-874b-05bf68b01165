/* @import "/components/colorUi/animation.scss"; */
/* @import "/components/colorUi/icon.css";
@import "/components/colorUi/main.css"; */
.page {
  height: 100%;
  font-family: PingFang SC, PingFang SC-Semibold;
}

view,
text {
  font-family: PingFang SC, PingFang SC-Semibold;
}

input {
  color:#E72410;
}

view,
scroll-view {
  box-sizing: border-box;
}

/* 是否显示滚动条 */
/* ::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
} 
 ::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 10rpx rgba(0, 0, 0, 0.3);
  box-shadow: inset 0 0 6rpx rgba(0, 0, 0, 0.1);
  border-radius: 10rpx;
  background-color: #fff;
}
 ::-webkit-scrollbar-thumb {
  border-radius: 10rpx;
  -webkit-box-shadow: inset 0 0 10rpx rgba(0, 0, 0, 0.3);
  box-shadow: inset 0 0 6rpx rgba(0, 0, 0, 0.1); 控制滚动条颜色
background-color: #39b54a; 
*/
.disabled {
  background-color: #9b9eac;
}

/*我自己常用的一些css  */

/* 显示省略号 */
.text-ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.text-num-ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  /* -webkit-line-clamp: 3; 自己加 */
  overflow: hidden;
}

/* 文字两端对其，只有两个那种就用flex来做 -- */
.textJustify {
  display: inline-block;
  text-align-last: justify;
  -moz-text-align-last: justify;
  -ms-text-align-last: justify;
  -webkit-text-align-last: justify;
}

image {
  width: 100%;
  height: 100%;
}

/* 无边框  */
.boder-none {
  border: none !important;
}

/* 清除x图标 */
.clearIcon {
  width: 32rpx;
  height: 32rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAA/UExURQAAAAAAAAAAAEVFRQAAANXV1QAAAEdwTBAQEPLy8kRERAAAAAAAAAAAAAAAAAAAANra2isrK7q6ur6+vv///20+0i0AAAAUdFJOU39oTpMT23QAg/OTIwQuCEbfi8nL3FdaYwAAANtJREFUOMuFk+sShSAIhMGorPFW+f7PeqhM0VPj/mLcb5pYAOYsZxVOABMq68orPAUpEFLUAMZDI28kQAh/QioATfCiiR7g3U8EAwbhQ2guwMOn/AnQXW+HLobet7sgBlL/IY6Z0GMMKY8ZXHoclkywv6zp2YGFlpA+2yXhRFQ+26LHi6h9tmVIJ1H70GTIRO230iHGoJvApc/fX0Qel421vw41gaLN9P81oUpQub+KsDlq0b8kXB7WIfpjYs/DyuMOov8hiHF3F6a/ct2l7a99/3D6p9c/3u/z/wGhjgzpIacRsQAAAABJRU5ErkJggg==') no-repeat;
  background-size: 100% 100%;
}

/* 按钮--中间就保存 */
.box-btn {
  position: absolute;
  bottom: 0;
  width: 750rpx;
  height: 168rpx;
  background: #ffffff;
  box-shadow: 8rpx 0rpx 8rpx 0rpx rgba(204, 204, 204, 0.20);
}

.box-btn .btn {
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
  margin-top: 10rpx;
  width: 702rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: #E72410;
  border-radius: 8rpx;
  font-size: 34rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 600;
  text-align: CENTER;
  color: #ffffff;
}

.box-btn .btm-btn {
  position: relative;
  margin-top: 10rpx;
  width: 702rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #FFB2AA 0%,#E72410 100%);
  border-radius: 8rpx;
  font-size: 34rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 600;
  color: #ffffff;
}



.flex {
  display: flex;
}

/* 居中的公共样式 */
.pos_r_center {
  position: relative;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.pos_a_center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.pos_a_l {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.pos_r_l {
  position: relative;
  left: 50%;
  transform: translateX(-50%);
}

.flex_center {
  display: flex;
  align-items: center;
}

.flex_all_center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex_btn {
  display: flex;
  justify-content: space-between;
}

.flex_btn_center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex_ard_center {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex_column {
  display: flex;
  flex-direction: column;
}

.flex_column_center {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.flex_column_btn {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.flex_column_ard {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.flex_start_center {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

/* 企业标签样式 */
text.darkBlue {
  background: rgba(7, 110, 228, 0.1);
  color: #E72410;
}

text.wathetBlue {
  background: rgba(74, 184, 255, 0.1);
  color: #4ab8ff;
}

text.yellow {
  background: rgba(255, 185, 62, 0.12);
  color: #ffb93e;
}

text.green {
  background: rgba(38, 200, 167, 0.1);
  color: #26c8a7;
}

text.purple {
  background: rgba(156, 133, 219, 0.12);
  color: #9c85db;
}

text.brown {
  background: rgba(203, 155, 155, 0.1);
  color: #cb9b9b;
}

text.onBusiness {
  background: rgba(1, 144, 225, 0.1);
  color: #0190e1;
}

text.comColor {
  background: rgba(0, 173, 211, 0.1);
  color: #00add3;
}

text.miniCom {
  background: rgba(228, 113, 7, 0.1);
  color: #e47107;
}

text.logOff {
  background: rgba(92, 96, 112, 0.1);
  color: #7f8498;
}

/* 没有定义到的 */
text.customColor {
  background: rgba(243, 100, 32, 0.1);
  color: #f36420;
}

/* loading 样式 */
.weui-loading {
  margin: 0 5px;
  width: 20px;
  height: 20px;
  display: inline-block;
  vertical-align: middle;
  -webkit-animation: weuiLoading 1s steps(12, end) infinite;
  animation: weuiLoading 1s steps(12, end) infinite;
  background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat;
  background-size: 100%;
}


/* 顶部筛选按钮样式 */
.jj-btn {
  border-radius: 4px;
  background: #F7F7F7;
  padding: 10rpx 20rpx 10rpx 19rpx;
  font-size: 25rpx;
  color: #5C6070;
}

.jj-btn::after {
  content: "";
  width: 0;
  height: 0;
  border-bottom: 10rpx solid #A0A5BA;
  border-left: 10rpx solid transparent;
  position: relative;
  bottom: 16rpx;
  left: 6rpx;
}

.jj-btn.active {
  background-color: #E6F2FF;
  color: #E72410;
  font-weight: 700;
}

.jj-btn.active::after {
  border-bottom: 10rpx solid #E72410;
}

/* 企业标签样式 */
.company-tag-wrap {
  display: flex;
  padding-left: 53rpx;
  font-size: 23rpx;
  margin-top: 15rpx;
  flex-wrap: wrap;
}

.company-tag-wrap text {
  flex-shrink: 0;
  padding: 6rpx 12rpx;
  border-radius: 4rpx;
  margin-right: 15rpx;
  margin-bottom: 10rpx;
}

.company-tag-wrap text.darkBlue {
  background: rgba(7, 110, 228, 0.10);
  color: #E72410;
}

.company-tag-wrap text.wathetBlue {
  background: rgba(74, 184, 255, 0.10);
  color: #4AB8FF;
}

.company-tag-wrap text.yellow {
  background: rgba(255, 185, 62, 0.12);
  color: #FFB93E;
}

.company-tag-wrap text.green {
  background: rgba(38, 200, 167, 0.10);
  color: #26C8A7;
}

.company-tag-wrap text.purple {
  background: rgba(156, 133, 219, 0.12);
  color: #9C85DB;
}

.company-tag-wrap text.brown {
  background: rgba(203, 155, 155, 0.10);
  color: #CB9B9B;
}

.company-tag-wrap text.onBusiness {
  background: rgba(1, 144, 225, 0.10);
  color: #0190E1;
}

.company-tag-wrap text.comColor {
  background: rgba(0, 173, 211, 0.10);
  color: #00ADD3;
}

.company-tag-wrap text.miniCom {
  background: rgba(228, 113, 7, 0.10);
  color: #E47107;
}

.company-tag-wrap text.logOff {
  background: rgba(92, 96, 112, 0.10);
  color: #7F8498;
}

.btn_area {
  width: 100%;
  height: 214rpx;
  background: #ffffff;
  display: flex;
  justify-content: space-around;
  /* align-items: center; */
  position: fixed;
  bottom: 0;
  border-top: 2rpx solid #f7f7f7;
}

.disabled {
  background-color: #9B9EAC;
}

.rounded-bg {
  overflow: hidden;
  border-radius: 8rpx;
  box-shadow: 0rpx 0rpx 8rpx 0rpx rgba(32, 38, 58, 0.20);
  box-shadow: 2rpx 2rpx 2rpx 0rpx #f2f2f2;
}

.rounded-small {
  overflow: hidden;
  border-radius: 4rpx;
  box-shadow: 0rpx 0rpx 8rpx 0rpx rgba(32, 38, 58, 0.20);
}

/* 测试  */
.red {
  border: 1px solid red !important;
}

.pink {
  border: 1px solid pink !important;
}

/* 弹窗公共样式 */


/* 原本样式  */
.fadeIn {
  -webkit-animation: c 0.3s forwards;
  animation: c 0.3s forwards;
}

@-webkit-keyframes c {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes c {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.mask {
  position: fixed;
  z-index: 99;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}