import {
  home,
  common
} from '../../../../service/api'
import {
  deteleObject
} from '../../../../utils/util'
import {
  moneyFormat
} from '../../../../utils/util'
import {
  getHeight
} from '../../../../utils/height';
import {
  handleSearchHight,
  tirms
} from '../../../../utils/formate';
import {
  getInvestmentEvent
} from '../../../../service/financing';
var financingBehavior = require('./components/behaviorConfig');

const app = getApp();
Page({
  behaviors: [financingBehavior],
  onReady() {
    this.getHeight();
  },
  data: {
    dropDownMenuTitle: ['所属地区', '所属产业', '投资时间'],
    oleDropDownMenuTitle: ['所属地区', '所属产业', '投资时间'],
    wordList: [],
    // 请求相关
    bazaarParms: {
      page_index: 1, //偏移量
      page_size: 10, //每页多少条
      org_id: '',
      com_name: '',
    },
    bazaarlist: [], //获取列表
    bazaarIsFlag: true, //节流 true允许
    bazaarHasData: true, //  是否还有数据
    bazaarIsNull: false, // list长度是否为0
    bazaarIsTriggered: false, // 下拉刷新状态
    count: 0, //数量
    isFocus: 0 //只是为了弹窗不共存
  },
  onLoad(e) {
    this.setData({
      'bazaarParms.org_id': e.org_id
    })
    this.bazaarRefresher()
    // app.showLoading()
    // // this.getHotWord()
    // this.getHisWord()
    // this.initGetList(() => {
    //   wx.hideLoading()
    // })
  },
  onShow() {},
  /********这里目前用不到-改版了*********/
  async confirm(e) { //点击搜索时触发 
    let val = e.detail.value
    if (val.trim()) {
      home.financingHis({
        keyword: tirms(val)
      }) //历史记录
    }
    this.setData({
      'bazaarParms.ent_name': val
    }, () => {
      this.bazaarRefresher()
      this.getHisWord()
    })
  },
  async getHisWord(list = []) { //历史次
    let {
      wordList,
      hotWord = []
    } = this.data
    let [err, res] = await app.to(home.financingGetHis())
    if (err) return
    // 还要做个处理 就是 根据name找到一样的删除原来的 push新来的
    res = res.reverse().map(item => {
      let len = item.key_word.length
      let val = len > 5 ? item.key_word.slice(0, 5) + '...' : item.key_word
      return {
        name: item.key_word,
        word: val
      }
    })
    if (list.length) {
      wordList = [...list, ...res]
    } else {
      wordList = [...hotWord, ...res]
    }
    wordList = deteleObject(wordList)
    // console.log(wordList)
    this.setData({
      wordList
    })
  },
  async getHotWord() { //热词 --如果要完全保證這個在前面就需要等历史记录发了再发这个
    let [err, res] = await app.to(home.financingRecommend())
    if (err) return
    res = res.map(item => {
      return {
        name: item.keyword,
        word: item.keyword
      }
    })
    this.setData({
      hotWord: res
    })
    this.getHisWord(res)
  },
  clickWord(e) { //点击热词
    let {
      item
    } = e.currentTarget.dataset
    this.setData({
      'bazaarParms.ent_name': item.name
    }, () => {
      this.bazaarRefresher()
    })
  },
  focus() { //聚焦时 
    this.setData({
      isSearPop: false,
      isFocus: ++this.data.isFocus
    })
  },
  /*****************/
  //获取列表
  initGetList(callback) {
    const that = this;
    let {
      bazaarlist,
      bazaarHasData,
      bazaarParms
    } = that.data;
    that.setData({
      bazaarIsNull: false,
      bazaarIsFlag: false,
      bazaarHasData: true
    });
    getInvestmentEvent(bazaarParms).then(res => {
      let {
        datalist,
        count
      } = res
      console.log(res)
      let ary = []
      if (datalist.length < bazaarParms.page_size) bazaarHasData = false;
      ary = handleSearchHight(datalist, 'com_name', bazaarParms.ent_name)
      ary = datalist.map((item, index) => {
        let moneyStr = '';
        if (item.invest_detail_money != '0' && item.invest_currency_name) {
          moneyStr = moneyFormat(item.invest_detail_money, item.invest_currency_name);
        } else {
          moneyStr = item.invest_similar_money_name || '未透露';
        }
        return {
          moneyStr,
          ...item
        };
      })
      that.setData({
        bazaarlist: bazaarlist.concat(ary),
        bazaarHasData,
        bazaarIsFlag: true,
        count
      }, () => {
        if (!that.data.bazaarlist.length) that.setData({
          bazaarIsNull: true,
          bazaarHasData: true
        });
      })
      callback && callback()
    }).catch(err => {
      callback && callback()
      app.showToast('获取数据失败!请稍后再试')
      that.setData({
        bazaarlist: [],
        bazaarIsNull: true,
        bazaarHasData: true,
        count: 0
      });
      console.log(err)
    })
  },
  gosearch(e) {
    console.log(e, this.data.bazaarParms.com_name)
    this.setData({
      'bazaarParms.com_name': e.detail.value
    }, () => {
      this.bazaarRefresher()
    })
  },
  inputcancel() {
    this.setData({
      'bazaarParms.com_name': ''
    }, () => {
      this.bazaarRefresher()
    })
  },
  // 下拉刷新
  bazaarRefresher() {
    const that = this;
    this.setData({bazaarIsNull:false})
    wx.showNavigationBarLoading()
    let {
      bazaarParms,
      type
    } = that.data
    let obj = {
      ...bazaarParms,
      page_index: 1,
      page_size: 10
    }
    that.setData({
      bazaarParms: obj,
      bazaarlist: [],
      bazaarHasData: true,
      bazaarIsNull:true,
    }, () => that.initGetList(() => {
      that.setData({
        bazaarIsTriggered: false
      })
      wx.hideNavigationBarLoading()
    }))
  },
  //加载更多
  bazaarloadMore() {
    let {
      bazaarParms,
      bazaarHasData,
      bazaarIsFlag
    } = this.data;
    if (!bazaarHasData) return;
    if (!bazaarIsFlag) return; //节流
    bazaarParms.page_index += 1;
    this.setData({
      bazaarParms
    }, () => this.initGetList());
  },
  // 筛选
  onFlitter(e) {
    let {
      dropDownMenuTitle,
      bazaarParms,
      bazaarlist
    } = this.data
    console.log(e.detail)
    const obj = e.detail;
    bazaarParms = {
      ...bazaarParms,
      page_index: 1,
      page_size: 10,
      ...obj
    }
    if (!bazaarParms['invest_date']) { //特殊處理--解決覆蓋問題
      delete bazaarParms['invest_date']
    }
    bazaarlist = []
    this.setData({
      bazaarParms: bazaarParms,
      bazaarlist,
      dropDownMenuTitle
    }, () => {
      app.showLoading('加载中...')
      this.initGetList(() => wx.hideLoading())
    })
  },
  getHeight() {
    const that = this;
    getHeight(that, ['.sci_line'], (data) => {
      let {
        res,
        screeHeight
      } = data
      let cardHeight = screeHeight - res[0]?.top - res[0]?.height
      that.setData({
        cardHeight
      })
    })
  },
  // 头部相关筛选事件
  openSearPop() {
    let bool = this.data.isSearPop ? false : true
    // let isFocus = bool ? true : false //这里只是为了关闭下面得弹窗-不让弹窗共存
    this.setData({
      isSearPop: bool,
      isFocus: ++this.data.isFocus
    })
  },
  tooltip(e) {
    let {
      item: {
        text
      }
    } = e.currentTarget.dataset
    app.showToast(text, 'none', 1200)
  },
  blur() {
    this.setData({
      isFocus: ++this.data.isFocus
    })
  },
  clickHeadList(e) { //点击头部筛选列表
    console.log(999)
    let {
      bazaarParms
    } = this.data
    let {
      item: {
        id
      }
    } = e.currentTarget.dataset
    if (id != '-1') {
      bazaarParms['optimization'] = id
    } else {
      bazaarParms['optimization'] = ''
    }
    this.setData({
      isSearPop: false,
      bazaarParms
    }, () => {
      this.bazaarRefresher()
    })
  },
  //去详情页面
  async goDetail(e) {
    let {
      event_id
    } = e.detail
    if (!event_id) {
      return
    }
    let url = `../detail/index?event_id=${event_id}`;
    app.route(this, url)
  },
})