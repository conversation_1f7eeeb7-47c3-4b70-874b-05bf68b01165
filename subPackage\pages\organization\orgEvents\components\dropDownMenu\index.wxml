<import src="/template/menuhead/index"></import>
<view class="sci_head">
  <template
    is='menu-head'
    data="{{selected_source_name,dropDownMenuTitle,source_open,district_open,filter_open,four_open,source_val,district_val,filter_val,four_val}}"
  >
  </template>
</view>

<!-- 所属地区 -->
<region-selection
  visible="{{district_open}}"
  top="{{top}}"
  bindsubmit="getRegion"
  bindclose="close"
  oldData="{{regionData}}"
/>

<!-- 所属产业source_open -->
<TagMultipleChoice
  visible="{{source_open}}"
  dataList="{{[businessList]}}"
  top="{{top}}"
  bindsubmit='getbusness'
  bindclose="close"
/>

<!-- 更多筛选 -->
<TagMultipleChoice
  visible="{{filter_open}}"
  dataList="{{[financingTime]}}"
  top="{{top}}"
  bindsubmit='handleMoreFilter'
  bindhandlefinancingTime='handlefinancingTime'
  bindclose="close"
>
  <view slot="financingTime" class="financingTime" >
    <view class="year" data-type="financingTime-viewStartTime" bindtap="showDatePicker">{{viewStartTime ||'开始时间'}}</view>
    <text class="short-line">—</text>
    <view class="year" data-type="financingTime-viewEndTime" bindtap="showDatePicker">{{viewEndTime || '结束时间'}}</view>
  </view>
</TagMultipleChoice>

<!-- 时间选择 -->
<date-picker
  visible="{{showPicker}}"
  _date="{{date}}"
  dateType="{{dateType}}"
  bindsetDate="setDate"
  title="{{title}}"
>
</date-picker>