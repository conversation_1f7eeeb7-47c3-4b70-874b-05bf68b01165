<import src="/template/menuhead/index"></import>
<view class="sci_head">
  <template
    is='menu-head'
    data="{{selected_source_name,dropDownMenuTitle,source_open,district_open,filter_open,four_open,source_val,district_val,filter_val,four_val}}"
  >
  </template>
</view>

<!-- 所属地区 -->
<region-selection
  visible="{{district_open}}"
  top="{{regionTop}}"
  bindsubmit="getRegion"
  bindclose="close"
  oldData="{{regionData}}"
/>

<!-- 所属产业source_open -->
<TagMultipleChoice
  visible="{{source_open}}"
  dataList="{{[leadingIndustryCode]}}"
  top="{{regionTop}}"
  bindsubmit='getbusness'
  bindclose="close"
/>

<!-- 融资轮次 -->
<TagMultipleChoice
  visible="{{filter_open}}"
  dataList="{{[parkLevel]}}"
  top="{{regionTop}}"
  bindsubmit='getfinancing'
  bindclose="close"
/>

<!-- 更多筛选 -->
<TagMultipleChoice
  visible="{{four_open}}"
  dataList="{{[policyCheckListCode, parkNature, serviceListCode]}}"
  top="{{regionTop}}"
  bindsubmit='handleMoreFilter'
  bindhandlefinancingTime='handlefinancingTime'
  bindclose="close"
>
</TagMultipleChoice>