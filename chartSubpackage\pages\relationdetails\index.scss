@import '/template/null/null.scss';

.rel-page {
    width: 100%;
    height: 100vh;
}

/* .rel-page .zhanwei {
    height: 0;
    width: 100%;
    border-top: 1px solid #f2f2f2;
} */

.rel-nav {
    height: 96rpx !important;
}

/* 关系图谱 */
.atlas {
    position: relative;
    border-top: 10px solid #f2f2f2;
    overflow: hidden;
}

/*关系图谱-- 按钮 */
.btn-group {
    position: fixed;
    display: flex;
    flex-direction: column;
    right: 32rpx;
    bottom: 88rpx;
    /* width: 80rpx; */
    height: 208rpx;
    background: #ffffff;
    border: 1rpx solid #eeeeee;
    border-radius: 8rpx;
    box-shadow: 0rpx 0rpx 8rpx 0rpx rgba(32, 38, 58, 0.10);
    padding: 0 16rpx;
    z-index: 1000;
}

.btn-group-item {
    position: relative;
    width: 100%;
    padding: 16rpx 0;
    border-bottom: 1rpx solid #eeeeee;
}

.btn-group-item view:nth-child(1) {
    width: 32rpx;
    height: 32rpx;
    margin-bottom: 8rpx;
}

.btn-group-item view:nth-child(2) {
    font-size: 24rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: center;
    /* color: #20263a; */
    line-height: 32rpx;
}

/* 分享按钮 */
.btn-group .share {
    display: inline-block;
    position: absolute;
    top: 0;
    left: -16rpx;
    width: 80rpx !important;
    height: 102rpx;
    opacity: 0;
}


/* 关系列表 */
.sol {
    background: #f2f2f2;
    width: 100%;
}

.sol .titles {
    padding: 28rpx 24rpx;
    line-height: 40rpx;
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #74798c;
    background: #F7F7F7;
}
.sol .titles-empty {
  height: 10px;
  background: #F7F7F7;
}

.sol .titles text {
    font-weight: 600;
    color:#E72410 !important;
    padding: 0 8rpx;
}

.solCard {
    background: #fff;
    border-bottom: 20rpx solid #f2f2f2;
}

.solCard .title {
    padding: 28rpx 24rpx 0;
    display: flex;
    align-items: center;
    background: #fff;
}

.solCard .title>image {
    width: 52rpx;
    height: 52rpx;
}

.solCard .title>text {
    line-height: 52rpx;
    font-size: 32rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 600;
    text-align: LEFT;
    color: #20263a;
    padding-left: 20rpx;
}

.solCard .con {
    margin: 28rpx 24rpx;
    background: rgba(74, 184, 255, 0.04);
    border-radius: 8rpx;
    border: 1rpx solid rgba(74, 184, 255, 0.1);
    /* 32 -( 60行高-40文字高度 / 2上下) */
    padding: 22rpx 24rpx;
}

.solCard .con text {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    color: #20263a;
    /*  */
    line-height: 60rpx;
}

.solCard .con .arrow-box {
    position: relative;
    display: inline-block;
    margin: 8rpx;
}

.solCard .con .line {
    /* 220-43 */
    min-width: 177rpx;
    height: 3px;
    border-top: 1rpx solid #a0a5ba;
}

.solCard .con .line-text {
    position: absolute;
    top: -14rpx;
    left: 50%;
    transform: translateX(-50%);
    /* min-width: 86rpx; */
    height: 28rpx;
    font-size: 20rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    color: #1E75DB;
    background: #f8fcff;
    text-align: center;
}

.arrow-box-ico-r {
    width: 10rpx;
    height: 16rpx;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAQBAMAAADdUfNzAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAnUExURUdwTKCmuqKiuqClu6CkurGxu6Glu6Cnu6GnuaCluqGluaCmuqCluo0JHyoAAAAMdFJOUwCfGPuCCOJLP9C77T+xqg8AAAA9SURBVAjXY2AAA8cAEHlmApg8pgAiz1iAyTMFYNIETJ4RBZMHweRJMJkEIk8pMIAlgeQRkK7DBSByB5AAACs4JUOgbPyJAAAAAElFTkSuQmCC') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 0;
    top: -8rpx;
}

.arrow-box-ico-l {
    width: 10rpx;
    height: 16rpx;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAQBAMAAADdUfNzAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTKGmuaCluqClu52xxKCgwKCluqClu6GpvaCluqGnuqCluqCluqKlu6GjuqClurGoRsoAAAAPdFJOUwA18+UNCJ9YIMyFx9dHb+XkSBcAAAA/SURBVAjXY2CAA2kQwa4PItf/BxJs/4Eky3kQ2fwfSDLag8im/yDyPpjkA5MM9WCSUR9EMgSDSZb5IJIh7D8A8TEl5zxGdhkAAAAASUVORK5CYII=') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    top: -8rpx;
}


/* 权限按钮 */