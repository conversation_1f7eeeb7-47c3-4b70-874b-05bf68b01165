import {
  home,
  common
} from '../../../../service/api'
import {
  getHeight
} from '../../../../utils/height';
import {
  preventActive
} from '../../../../utils/util';
import {
  debounce,
  formatDate,
  handleSearchHight
} from '../../../../utils/formate';
import {
  collect
} from '../../../../utils/mixin/collect'
const app = getApp();
Page({
  onLoad(option) {
    let {
      park_name,
      region_code
    } = JSON.parse(decodeURIComponent(option.obj))
    wx.setNavigationBarTitle({
      title: park_name,
    })
    this.setData({
      'bazaarParms.park_name': park_name,
      'bazaarParms.region_code': region_code
    }, () => {
      app.showLoading()
      this.initGetList(() => {
        wx.hideLoading()
      })
    }) //显示数量，一个用于发请求 
  },
  data: {
    dropDownMenuTitle: ['所属地区', '行业类型', '更多筛选'],
    // 请求相关
    bazaarParms: {
      page_index: 1, //偏移量
      page_size: 10, //每页多少条
      park_name: '',
      region_code: '',
    },
    bazaarlist: [], //获取列表
    bazaarIsFlag: true, //节流 true允许
    bazaarHasData: true, //  是否还有数据
    bazaarIsNull: false, // list长度是否为0
    bazaarIsTriggered: false, // 下拉刷新状态
    count: 0
  },
  onReady() {
    this.getHeight()
  },
  //获取小米列表
  initGetList(callback) {
    const that = this;
    let {
      bazaarlist,
      bazaarHasData,
      bazaarParms
    } = that.data;
    that.setData({
      bazaarIsNull: false,
      bazaarIsFlag: false,
      bazaarHasData: true
    });
    home.parkEntList(bazaarParms).then(res => {
      let {
        datalist,
        count
      } = res
      let arr = []
      if (datalist.length < bazaarParms.page_size) bazaarHasData = false;
      datalist.map((item, index) => {
        arr.push({
          register_date: formatDate(item.esdate, 'yyyy-MM-dd'),
          dom: item.dom,
          ent_name: item.entname,
          ent_id: item.entid,
          logo: item.ent_logo,
          collect: item.is_sc,
          legal_person: item.faren,
          register_capital: item.regcap,
          tags: item.tags
        })
      })

      that.setData({
        bazaarlist: bazaarlist.concat(arr),
        bazaarHasData,
        bazaarIsFlag: true,
        count
      }, () => {
        if (!that.data.bazaarlist.length) that.setData({
          bazaarIsNull: true,
          bazaarHasData: true,
          count: 0
        });
      })
      callback && callback()
    }).catch(err => {
      callback && callback()
      app.showToast('获取数据失败!请稍后再试')
      that.setData({
        bazaarlist: [],
        bazaarIsNull: true,
        bazaarHasData: true,
        count: 0
      });
      console.log(err)
    })
  },
  // 下拉刷新
  bazaarRefresher() {
    const that = this;
    wx.showNavigationBarLoading()
    let {
      bazaarParms,
    } = that.data
    let obj = {
      ...bazaarParms,
      page_index: 1,
      page_size: 10,
    }
    that.setData({
      bazaarParms: obj,
      bazaarlist: [],
      bazaarHasData: true,
    }, () => that.initGetList(() => {
      that.setData({
        bazaarIsTriggered: false
      })
      wx.hideNavigationBarLoading()
    }))
  },
  //加载更多
  bazaarloadMore() {
    let {
      bazaarParms,
      bazaarHasData,
      bazaarIsFlag
    } = this.data;
    if (!bazaarHasData) return;
    if (!bazaarIsFlag) return; //节流
    bazaarParms.page_index += 1;
    this.setData({
      bazaarParms
    }, () => this.initGetList());
  },
  // 筛选
  onFlitter(e) {
    let {
      dropDownMenuTitle,
      bazaarParms,
      bazaarlist,
      type
    } = this.data
    const obj = e.detail;
    dropDownMenuTitle[0] = obj.name1
    dropDownMenuTitle[1] = obj.name2,
      delete obj['name1'];
    delete obj['name2'];
    bazaarParms = {
      ...bazaarParms,
      page_index: 1,
      page_size: 10,
      ...obj
    }
    bazaarlist = []
    this.setData({
      bazaarParms: bazaarParms,
      bazaarlist,
      dropDownMenuTitle
    }, () => {
      // app.showLoading('加载中...')
      // this.initGetList(() => wx.hideLoading())
      this.bazaarRefresher()
    })
  },
  async onCard(data) {
    preventActive(this, async () => {
      // 地址  "site"  联系方式 "relation" 官网 "official" 收藏 "collect" 四个字段对呀四个方法逻辑-具体见设计图
      let that = this
      let {
        bazaarlist
      } = this.data
      const type = data.detail.type
      const comDetail = data.detail.data
      const index = bazaarlist.findIndex(item => item.ent_id == comDetail.ent_id) //comDetail.index
      // console.log(index)
      // 处理收藏 
      if (type == 'collect') {
        
        collect(that, comDetail, 'bazaarlist')
      } else if (type == 'relation') {
        let contactRes = await common.contact(comDetail.ent_id, encodeURI("types=1,2"))
        console.log("联系方式res", contactRes);
        if (contactRes.length > 0) {
          this.setData({
            contactList: contactRes,
            showContact: true
          })
        } else {
          app.showToast('暂无联系方式', 'none', 1300)
          this.setData({
            contactList: [],
            showContact: false
          })
        }
      } else if (type === 'site') {
        // this.setData({
        //   location: {
        //     lat: +comDetail.location.lat,
        //     lon: +comDetail.location.lon,
        //   },
        //   locationTxt: comDetail.register_address,
        //   addmarkers: [{
        //     id: 1,
        //     latitude: +comDetail.location.lat,
        //     longitude: +comDetail.location.lon,
        //     iconPath: "https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/marker.png",
        //     width: 20,
        //     height: 20,
        //   }],
        //   showAddress: true,
        //   locationMap: {
        //     latitude: +comDetail.location.lat, //维度
        //     longitude: +comDetail.location.lon, //经度
        //     name: comDetail.register_address, //目的地定位名称
        //     scale: 15, //缩放比例
        //     address: comDetail.register_address //导航详细地址
        //   }
        // })
        console.log(bazaarlist[index].dom)
        if (bazaarlist[index].dom) {
          wx.getLocation({
            //定位类型 wgs84, gcj02
            type: 'gcj02',
            success: function (res) {
              console.log("定位信息", res);
              var url = 'https://apis.map.qq.com/ws/geocoder/v1/?address=' + bazaarlist[index].dom + '&key=EUPBZ-6MD3K-I6OJB-ALA4P-DZGD3-54F67';
              wx.request({
                url: url,
                success: function (res) {
                  var location = res.data.result.location;
                  wx.openLocation({
                    address: bazaarlist[index].dom,
                    name: bazaarlist[index].dom,
                    longitude: +location.lng,
                    latitude: +location.lat,
                    scale: 18
                  })
                }
              })
            },
          })
        }
      }
    })
  },
  goMap() {
    const {
      locationMap
    } = this.data
    wx.openLocation(locationMap)
  },
  onCloseContact() {
    this.setData({
      showContact: false
    })
  },
  onCloseAddress() {
    this.setData({
      showAddress: false
    })
  },
  makeCall(e) {
    const item = e.target.dataset['item'] || e.currentTarget.dataset['item']
    // console.log(item.contact_data)
    wx.makePhoneCall({
      phoneNumber: item.contact_data,
    })
  },
  // 去详情页面 
  goDetail(e) {
    let {
      enterprise_id
    } = e.currentTarget.dataset.item
    // let url = `/subPackage/pages/companyDetail/companyDetail?id=${enterprise_id}`;
    // app.route(this, url)
    const url = encodeURIComponent(`https://reporth5.handidit.com?entId=${enterprise_id}`)
    app.route(this, `/subPackage/pages/webs/index?url=${url}`)
  },
  getHeight() {
    const that = this;
    getHeight(that, ['.sci_line'], (data) => {
      let {
        res,
        screeHeight
      } = data
      let cardHeight = screeHeight - res[0]?.top - res[0]?.height
      that.setData({
        cardHeight
      })
    })
  }
})