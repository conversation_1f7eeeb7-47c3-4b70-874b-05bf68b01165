<!-- 业务领域 -->
<HalfScreenPop showCloseBtn="{{false}}" visible="{{visible}}" position="{{position}}" bindsubmit="submit" bindclose="close" startDistance="{{startDistance}}" disableAnimation="{{true}}" _maskClosable="{{false}}" zIndex="{{10}}" footHeigh="110rpx">
  <view slot="customContent" class="area" style="margin-top:{{top}}px;">
    <view class="region-wrap">
      <scroll-view scroll-y style="height: 100%">
        <view style="display: flex;">
          <!-- 第一行 -->
          <view class="list province-list">
            <view class="item province {{item.active && 'active'}}" wx:for="{{orginList.leftList}}" wx:key="index" catchtap="checkItem" data-item="{{item}}">
              <text class="{{item.status && 'checked'}}">{{item.text}}</text>
              <text class="checkbox {{item.status}}"></text>
            </view>
          </view>
          <!-- 第二行 -->
          <view class="list city-list">
            <view class="item city {{item.active && 'active'}}" wx:for="{{orginList.centerList}}" wx:key="index" catchtap="checkItem" data-item="{{item}}">
              <text class="{{item.status && 'checked'}}">{{item.text}}</text>
              <text class="checkbox {{item.status}}" wx:if="{{item.text}}"></text>
            </view>
          </view>
          <!-- 第三行 -->
          <view class="list area-list">
            <view class="item area" wx:for="{{orginList.rightList}}" wx:key="index" catchtap="checkItem" data-item="{{item}}">
              <text class="{{item.status && 'checked'}}">{{item.text}}</text>
              <text class="checkbox {{item.status}}"></text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</HalfScreenPop>