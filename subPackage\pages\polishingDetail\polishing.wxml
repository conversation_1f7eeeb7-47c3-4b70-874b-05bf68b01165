<!--  style="padding-bottom: {{isIphoneX?'168rpx':'100rpx'}};" -->
<view class="artics">
  <!--  文章  -->
  <view class="artic_cont">
    <!--头部 -->
    <view class="arti_head">
      <view class="artic_head_t">{{item.research_name}}</view>
      <view class="artic_head_c">
        <view class="artic-subt">
          <view class="one">发文机关：{{item.source}}</view>
          <view class="two">{{item.create_time}}</view>
        </view>
        <view class="tags" wx:if="{{item.tags.length}}">
          <block wx:for="{{item.tags}}">
            <view class="{{'tag'+index}}">{{item}}</view>
          </block>
        </view>
      </view>
    </view>

    <!-- 报告下载 -->
    <view class="artic_about" wx:if="{{item.file}}">
      <view class="title">报告下载</view>
      <view class="dwonCont">
        <view class="dwonCont-l">
          <view class="dwonCont-one">
            <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/zs3-dzzs/bg3.png" class="img1"></image>
            <view class="bq">
              <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/subPackage/image/cy/{{item.est}}.png" class="img" style="width: {{item.est=='ptag4'?'270rpx':item.est=='ptag3'?'240rpx':'152rpx'}};"></image>
            </view>
          </view>
          <view class="dwonCont-two">
            {{item.research_name}}
          </view>
        </view>
        <view class="dwonCont-r" bindtap="openExcel" data-item="{{item}}">
          <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/subPackage/image/cy/down-w.png" class="imgd"></image>
          <view>打开报告</view>
          <view style="padding-left:8rpx;">{{item.size}}M</view>
        </view>
      </view>
    </view>
    <!-- 同类报告推荐 -->
    <view class="artic_about" wx:if="{{aboutList.length}}">
      <view class="title">同类报告推荐</view>
      <view class="cardCont">
        <block wx:for="{{aboutList}}" wx:key="{{index}}">

          <view class="icard" style="{{index==(aboutList.length-1)&&'border: none;'}}" bindtap="goDetail" data-item="{{item}}">
            <!-- 图片 -->
            <view class="dwonCont-card" style="width:{{item.est=='ptag4'?'164rpx':'144rpx'}}">
              <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/zs3-dzzs/bg2.png" class="imgBg"></image>
              <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>/{{item.est}}.png" class="img" style="width: {{item.est=='ptag4'?'164rpx':item.est=='ptag3'?'146rpx':'92rpx'}};"></image>
            </view>
            <!-- 标签 -->
            <view class="icard-r">
              <view class="icard-r-tit">{{item.research_name}}</view>
              <view class="icard-r-t">
                <view class="p-tags">
                  <!-- po1 po2 po3 -->
                  <block wx:for="{{item.tag}}" wx:key="{{index}}">
                    <view class="{{'po'+(index+1)}}">{{item}}</view>
                  </block>
                </view>
                <view class="pbtm">
                  <view class="two">{{item.create_time}}</view>
                  <view class="one">{{item.source}}</view>
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>
  </view>
</view>