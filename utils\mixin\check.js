const app = getApp();
module.exports = function (params) {
  // 根据params 自己在做一些特殊的混入
  return Behavior({
    data: {
      login: app.globalData.login,
    },
    pageLifetimes: {
      show() {
        this.handleLoginStatus()
      }
    },
    methods: {
      handleLoginStatus() {
        const {
          login
        } = app.globalData;
        if (login !== this.data.login) {
          this.setData({
            login
          });
        }
        // 登录退出时的处理 -相当于混入了2个钩子
        login ? this?.handleLogin && this.handleLogin() : this?.handleLogout && this.handleLogout();
      }
    }
  })
}