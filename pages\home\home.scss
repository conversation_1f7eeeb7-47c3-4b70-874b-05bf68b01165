@import '/template/null/null.scss';
@import '/template/more/more.scss';
@import '/template/home/<USER>/index.scss';
@import './mixin.scss';
@import '/template/tabBar/index.scss';

.relative {
  position: relative;
}

.mgtop20 {
  margin-top: 20rpx;
}

.ent-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  flex-shrink: 0;
  border: 1rpx solid #EEEEEE;
  box-sizing: border-box;
}

.wx-swiper-dots .wx-swiper-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
}

.home-wrap {
  position: relative;
  width: 100%;
  height: 100vh;
  /* overflow: hidden; */
  overflow-y: scroll;
}

.home {
  width: 100%;
  overflow: hidden;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

/* 首页头部部分 */
.h-head-bg {
  position: absolute;
  width: 750rpx;
  height: 438rpx;
  left: 0;
  top: 0;
  /* border: 1px solid red; */
}

.h_head {
  position: relative;
  width: 100%;
  height: 350rpx;
  background: rgba(231, 36, 16, 1);
  padding-top: 1px;
}

.h_head image {
  width: 100%;
  height: 100%;
}


.h_head .head_bg {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
}

.h_head .head_wz {
  position: relative;
  width: 324rpx;
  height: 52rpx;
  margin: 64rpx auto 64rpx;
}

.h_head .hd_c {
  /* border: 1px solid blue; */
  padding-left: 18rpx;
  padding-right: 18rpx;
  width: 542rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* border: 1px solid red; */
  margin: 0 auto 24rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
}



.hd_c_c {
  width: 128rpx;
  height: 58rpx;
  text-align: center;
  padding-top: 4rpx;
}

.hd_c_c .txt {
  position: relative;
  z-index: 10;
}

.tesu {
  position: relative;
}

.tesu .txt {
  font-weight: 600;
  color: #E72410;
  font-size: 32rpx;
}

.hd_c .line {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.h_head_input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 702rpx;
  height: 88rpx;
  background: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0px 2rpx 12rpx 0px rgba(184, 0, 0, 0.2), 0px 4px 24px 0px rgba(184, 0, 0, 0.1);
  padding: 0 0 0 24rpx;
  position: relative;
  margin: 24rpx auto 0;
  z-index: 1;
}

.h_head_input_l {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
  flex: 1;
}

.h_head_input_r {
  position: relative;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 160rpx;
  /* border: 1px solid red; */
  padding: 0 24rpx;
}

.h_head_input_r::after {
  content: " ";
  width: 2px;
  height: 40rpx;
  background: #eee;
  position: absolute;
  top: 50%;
  left: 0;
  transform: scaleX(0.5) translateY(-50%);
}

.h_head_input_r text {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(231, 36, 16, 1);
  line-height: 100rpx;
}

.h_head_input_r image {
  width: 24rpx;
  height: 24rpx;
  margin-left: 4rpx;
}

.h_search {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.h_nav_box {
  position: relative;
  background-color: #fff;
}

.h_nav {
  display: flex;
  flex-wrap: wrap;
  margin: 0 9rpx 40rpx;
}

.h_head .h_nav {
  position: relative;
}

.h_nav_item {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
}

.h_nav_img {
  width: 80rpx;
  height: 80rpx;

}

.h_head .h_nav_img {
  height: 90rpx;
}

.h_head .h_nav_img image {
  width: 80rpx;
  height: 80rpx;
}

.h_nav_name {
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263a;
  /* padding-top: 12rpx; */
}

.h_head .h_nav .h_nav_name {
  color: #fff;
}

.content {
  /* height: calc(100vh - 548rpx); */
  padding: 1rpx 24rpx 20rpx;
  background-color: #fff;
}

.bus-card {
  background-color: #F7F7F7;
}

.bimg {
  width: 100%;
  height: 160rpx;
  padding: 0 24rpx;
  margin-bottom: 24rpx;
}

.bimg image {
  width: 702rpx;
  height: 100%;
}

/* dialog文字样式 */
.dialog-con {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #74798c;
}

.dialog-con .map {
  position: relative;
  width: 100%;
  height: 112rpx;
  line-height: 112rpx;
  text-align: center;
  font-size: 34rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #E72410;
}

.weui-dialog__title {
  font-weight: 500 !important;
  color: #000000 !important;
  line-height: 40rpx;
  font-size: 34rpx !important;
}

.light_bd {
  padding: 0 !important;
}

.dialog-con .map::after {
  content: " ";
  width: 200%;
  height: 1rpx;
  background: #E5E5E5;
  position: absolute;
  top: 0;
  left: 0%;
  transform: scaleY(0.5);
}

.dialog-con .cancel {
  position: relative;
  height: 112rpx;
  line-height: 112rpx;
  text-align: center;
  font-size: 34rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
}

.dialog-con .cancel::after {
  content: " ";
  width: 200%;
  height: 1rpx;
  background: #E5E5E5;
  position: absolute;
  top: 0;
  left: 0%;
  transform: scaleY(0.5);
}

.home .search-card {
  padding: 32rpx 0 16rpx;
  box-shadow: 0px 4px 18px 0px rgba(221, 221, 221, 0.5000);
  border-radius: 8rpx;
}

.home .search-card .card-boxs {
  padding: 0 24rpx;
}

.home .search-card .card_ico {
  padding: 16rpx 24rpx 0;
}

/* 覆盖掉混入的代码 */
.weui-navbar {
  position: relative;
  height: 88rpx;
  display: flex;
  align-content: flex-start;
  width: 100%;
  font-size: 28rpx;
  border-bottom: 1rpx solid #EEEEEE;
  background: #fff;
}

.weui-navbar__item {
  display: flex;
  flex: 1;
  text-align: center;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 600;
  color: #74798C;
  justify-content: center;
  align-items: center;
}

.weui-navbar__item.weui-bar__item_on {
  font-weight: 600;
  color: #E72410;
}

.weui-navbar__slider::after {
  position: absolute;
  content: "";
  width: 40rpx;
  height: 6rpx;
  background: rgba(231, 36, 16, 1);
  border-radius: 8rpx;
  left: 50%;
  bottom: 0rpx;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}

/* 未登录按钮 */
.noLbtn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 750rpx;
  height: 260rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.noLbtn .img {
  position: absolute;
  width: 750rpx;
  height: 260rpx;
  bottom: 0;
  left: 0;
  z-index: 10;
}

.noLbtn .btn {
  position: relative;
  z-index: 11;
  width: 340rpx;
  height: 80rpx;
}
/* 25.3.4 */
.fixed_ico {
    position: fixed;
    right: 24rpx;
    bottom: 258rpx;
    width: 116rpx;
    height: 160rpx;
}