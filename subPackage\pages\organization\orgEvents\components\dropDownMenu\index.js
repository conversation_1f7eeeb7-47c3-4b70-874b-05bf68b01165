import {
  handleMultiple
} from '../../../../../../components/hunt/mixin'
var behavior = require('../../../../../../template/menuhead/index');
var financingBehavior = require('../behaviorConfig');
const app = getApp()
Component({
  behaviors: [financingBehavior, behavior],
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多个 slot 支持
  },
  properties: {
    isFocus: { //外面是否聚焦
      type: String,
      observer(val) {
        if (val) {
          this.closeHyFilter()
        }
      }
    }
  },
  data: {
    regionData: [], //选中的地区
    title: '开始时间',
    startTime: '', // 开始时间
    endTime: '', // 结束时间
  },
  observers: {
    regionData: function (list) {
      if (list.length > 0) {
        this.setData({
          district_open: true
        })
      }
    },
  },
  lifetimes: {
    ready() {
      this._getFilterItems();
    },
    created() {},
  },
  methods: {
    close(e) {
      const {
        financingTime,
        financingTime: {
          startTime,
          endTime
        }
      } = this.data;
      // 点击取消的时候将修改之前的数据重新赋值给当前展示用的变量
      this.setData({
        viewStartTime: startTime, // 注册年限开始时间(页面展示用: 根据点击取消时regTime.startTime的状态进行数据更新)
        viewEndTime: endTime, // 注册年限结束时间(页面展示用: 根据点击取消时regTime.endTime的状态进行数据更新)
        'financingTime.list': financingTime.prevList
      });
      this.closeHyFilter();
    },
    // 所属产业 
    getbusness(e) {
      let {
        source_val
      } = this.data
      const detail = e.detail[0]
      let data = detail.codeList.length && JSON.parse(JSON.stringify(detail.codeList)) || [];
      if (data.length > 0) {
        source_val = true
      } else {
        source_val = false
      }
      this.setData({
        'businessList.cur': data,
        source_val
      }, () => {
        this.backAll()
      });
    },
    // 地区 
    getRegion(e) {
      let data = JSON.parse(JSON.stringify(e.detail)); //解決checked狀態丟失的情況--拷貝--切記
      let text = ''
      let {
        regionData,
        district_val,
      } = this.data;
      regionData = data.length ? data : []
      district_val = regionData.length > 0;
      this.setData({
        regionData,
        district_val,
      }, () => this.backAll());
    },
    // 融资轮次 
    getfinancing(e) {
      let {
        filter_val
      } = this.data;
      const detail = e.detail[0]
      let data = detail.codeList.length && JSON.parse(JSON.stringify(detail.codeList)) || [];
      if (data.length > 0) {
        filter_val = true
      } else {
        filter_val = false
      }
      this.setData({
        'financing.cur': data,
        filter_val
      }, () => {
        this.backAll()
      })
    },
    // 更多筛选
    handleMoreFilter(e) {
      let {
        filter_val,
        viewStartTime,
        viewEndTime,
      } = this.data;
      const detail = e.detail
      detail.map((item) => {
        let data = item?.codeList && JSON.parse(JSON.stringify(item.codeList)) || [];
        if ((viewStartTime || viewEndTime) && item.id === "financingTime") data = [viewStartTime, viewEndTime];
        this.setData({
          [item.id]: {
            ...this.data[item.id],
            list: JSON.parse(JSON.stringify(item.allList)),
            cur: data,
            filter_val
          },
          [`${item.id}.prevList`]: this.data[item.id].canInput ? JSON.parse(JSON.stringify(item.allList)) : []
        });
      });
      filter_val = detail.some((data) => data.codeList.length > 0) || viewStartTime || viewEndTime;
      this.setData({
        filter_val,
        'financingTime.startTime': viewStartTime,
        'financingTime.endTime': viewEndTime
      }, () => this.backAll())
    },
    // 总的返回
    backAll() {
      const requestData = {
        cat_name: '',
        region_id: '',
        invest_date: ''
      };
      const {
        regionData,
        businessList,
        financing,
        financingTime,
        viewStartTime,
        viewEndTime,
        stageName,
        optimization
      } = this.data;

      // 业务领域 ---這裏有點特殊
      if (businessList.cur.length) {
        requestData['cat_name'] = businessList.cur.join(',')
      }
      // 融资轮次
      if (financing.cur.length) {
        requestData['invest_round_name'] = financing.cur

      }

      // 所处阶段
      if (stageName.cur.length) {
        requestData['stage_name'] = stageName.cur
      }

      // 产业优选
      if (optimization.cur.length) {
        requestData['optimization'] = optimization.cur[0]
      }

      // 融资时间 
      let time = financingTime.cur
      if (viewStartTime || viewEndTime) {
        const start = viewStartTime ? viewStartTime : '';
        const end = viewEndTime ? viewEndTime : '';
        requestData['invest_date'] = start + ',' + end
      } else {
        requestData['invest_date'] = (financingTime.cur.length && time[0] !== 'all') ? (time[0] + '-01-01' + ',' + time[0] + '-12-31') : ''
      }
      // 地区
      if (regionData.length > 0) {
        let temp = [];
        temp = handleMultiple(regionData).filter(item => item.status === 'checked').map(item => item.code)
        requestData['region_id'] = temp.length ? temp.join(',') : ''
      }
      this.closeHyFilter();
      this.triggerEvent('submit', requestData)
    },
    // 自定义日期额外操作
    handlefinancingTime(e) {
      this.setData({
        viewStartTime: '',
        viewEndTime: ''
      })
    },
    // 打开日期弹窗
    // 打开日期弹窗
    showDatePicker({
      currentTarget: {
        dataset: {
          type
        }
      }
    }) {
      const {
        data
      } = this;
      const timeType = type.split('-')[1];
      this.setData({
        showPicker: true,
        backfillDate: data[timeType] || '',
        dateType: type,
        title: timeType == 'viewStartTime' ? '开始时间' : '结束时间'
      })
    },
    //获取时间
    //获取时间
    setDate({
      detail: {
        date,
        dateType
      }
    }) {
      const {
        viewStartTime,
        viewEndTime
      } = this.data;
      if (date && !date.includes(undefined)) {
        const {
          data
        } = this;
        const temp = dateType.split('-');
        const list = data[temp[0]].list.map((item) => {
          item.status = '';
          return item
        });
        if (!viewStartTime && !viewEndTime) {
          this.setData({
            [temp[1]]: date,
            [`${temp[0]}.list`]: list
          });
        }
        if (viewStartTime !== undefined || viewEndTime !== undefined) {
          const startTime = temp[1] === 'viewStartTime' ? date : viewStartTime;
          const endTime = temp[1] === 'viewEndTime' ? date : viewEndTime;
          if (this.compareTime(startTime, endTime) < 0) {
            app.showToast('开始时间不能大于结束时间')
          } else {
            this.setData({
              [temp[1]]: date,
              [`${temp[0]}.list`]: list
            });
          }
        }
      }
    },
  },
})