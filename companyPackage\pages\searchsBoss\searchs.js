import constant from '../../../utils/constant'
import {
  home,
  boss
} from '../../../service/api';
import {
  getHeight
} from '../../../utils/height';
import {
  debounce,
  formatDate,
  handleSearchHight
} from '../../../utils/formate';
import {
  handlestructure
} from '../../../components/hunt/mixin';
const app = getApp();
Page({
  data: {
    isLogin: false,
    // 搜索相关
    inputShowed: false, //是否聚焦
    ent_name: "", //搜索值
    ents_name: "",
    historyList: [],
    browsingHistory: [], //浏览历史
    timer: null,
    // 弹窗相关 
    popType: '',
    showVisible: false, //是否显示弹窗 
    title: '',
    content: '',
    cancelBtnText: "取消",
    confirmBtnText: "删除",
    // 浏览历史高度 
    scrollHeight: 'auto',
    filtrateHeight: 'auto',
    statusBarHeight: 'auto',
    // 筛选 
    dropDownMenuTitle: ['所属地区', '所属行业'],
    // 卡片 下拉加载更多 
    cardHeight: 'auto',
    // 请求相关
    bazaarParms: {
      page_index: 1, //偏移量
      page_size: 10, //每页多少条
    },
    bazaarlist: [], //获取列表
    bazaarIsFlag: true, //节流 true允许
    bazaarHasData: true, //  是否还有数据
    bazaarIsNull: false, // list长度是否为0
    bazaarIsTriggered: false, // 下拉刷新状态
    // 高级搜索参数 
    heightParams: {},
    activeEntId: '', // 当前点击企业的id
    entUnlogin: false, // 是否是在未登录的情况点击了企业标题
  },
  onShow() {
    const {
      login
    } = app.globalData
    this.setData({
      isLogin: login
    }, () => {
      // login && app.showLoading('加载中')
      login && Promise.all([this.getSearchHisList(), this.getBevHisList()])
        .then(res => {
          // wx.hideLoading()
          this.scrollH()
        }).catch(err => {
          // wx.hideLoading()
        })
    })
    let ent_name = wx.getStorageSync('ent_name')
    if (ent_name) {
      this.setData({
        ent_name,
        ents_name: ent_name
      }, () => {
        this.addHistorySearch(ent_name)
        wx.removeStorageSync('ent_name')
      })
    }
    const {
      entUnlogin
    } = this.data;
    entUnlogin && this.bazaarRefresher();
  },
  // input相关--onblur后面真机看是否保留
  onConfirm: function (e) {
    let keyword = e.detail.value;
    const {
      isLogin
    } = this.data
    if (keyword.trim()) {
      isLogin && this.addHistorySearch(keyword);
      // 发请求跟线上同步---这里有个问题，游客的时候不会发请求，登录后和游客这里是不一样的  
      isLogin && home.addHistory({
        keyword,
        model_type: 'BOSS'
      })
    }
    // 没用onInput 
    if (keyword || keyword == '') {
      this.setData({
        ent_name: keyword,
      });
      app.showLoading('加载中');
      this.inputQuest()
    }
  },
  addHistorySearch(value) {
    let historySearch = wx.getStorageSync(constant.HistorySearch) || []
    let has = historySearch.includes(value);
    if (has) {
      let index = historySearch.findIndex(item => item == value);
      if (index == 0) return;
      historySearch.splice(index, 1)
    }
    let len = historySearch.length;
    if (len >= 10) {
      historySearch.pop();
    }
    historySearch.unshift(value);
    wx.setStorageSync(constant.HistorySearch, historySearch)
    this.setData({
      historyList: historySearch
    })
  },
  onClear() {
    this.unLocked()
    this.setData({
      ent_name: '',
      ents_name: '',
    })
    // x了不请求 
    // this.inputQuest('suspend') 
  },
  init() {
    this.setData({
      ent_name: "",
      ents_name: '',
      inputShowed: false
    });
  },
  isBlur() {
    this.setData({
      inputShowed: true
    });
  },
  onBlur() {
    // 拿到ent_name --传入最近搜索历史 
    const ent_nameue = this.data.ent_name
    if (ent_nameue.trim().length > 0) {
      this.addHistorySearch(ent_nameue)
    } else {
      this.setData({
        inputShowed: false
      });
    };

  },
  onInput: debounce(function ([...e]) {
    let keyword = e[0].detail.value;
    if (keyword || keyword == '') {
      this.setData({
        ent_name: keyword,
      });
      app.showLoading('加载中');
      this.inputQuest()
    }
  }),
  goBack() {
    this.unLocked()
    this.init()
    app.route(this, null, 'navigateBack')
  },
  unLocked() {
    wx.hideKeyboard();
    this.setData({
      loading: false,
      inputShowed: false
    })
  },
  inputQuest(suspend) {
    //处理input的数据到请求参数里面--过滤
    const {
      ent_name,
      bazaarParms
    } = this.data;
    bazaarParms['ent_name'] = ent_name
    bazaarParms.page_index = 1;
    this.setData({
      bazaarParms,
      bazaarlist: [],
      count: 0
    });
    app.showLoading('加载中');
    // !suspend &&
    this.initGetList(() => {
      wx.hideLoading();
      //完成之后--浏览历史
    })
  },
  // 点击最近搜索
  historyTap(e) {
    const keyword = e.target.dataset['item']
    this.setData({
      inputShowed: true,
      ent_name: keyword,
      ents_name: keyword
    }, () => this.inputQuest())
  },
  // 点击删除图标 
  handleIcon(e) {
    const type = e.currentTarget.dataset['index']
    const that = this;
    switch (type) {
      // 情况最近搜索
      case 'a':
        wx.showModal({
          title: '删除搜索',
          content: '确定要删除最近搜索?',
          success: function (res) {
            if (res.confirm) {
              wx.removeStorageSync(constant.HistorySearch)
              that.setData({
                showVisible: false,
                historyList: []
              })
              // 发请求同步
              home.clearHistory('BOSS')
            }
          }
        })
        break;
      case 'b': //清空浏览历史
        wx.showModal({
          title: '删除浏览历史',
          content: '确定要删除最近搜索?',
          success: function (res) {
            if (res.confirm) {
              // 发送请求 --成功删除历史
              home.detBevHis('BOSS').then(res => {
                app.showToast('删除成功!')
                that.setData({
                  browsingHistory: []
                })
              })
            }
          }
        })
        break;
      default:
        break;
    }
  },
  onClose() {
    this.setData({
      showVisible: false
    })
  },
  async getSearchHisList() { //获取搜索历史 列表
    let arr = [];
    home.getHistory('BOSS').then(res => {
      if (res && res.length > 0) {
        arr = res.map(item => item.key_word)
        arr = [...new Set(arr)]
      }
      wx.setStorageSync(constant.HistorySearch, arr)
      this.setData({
        historyList: arr
      }, () => {
        this.scrollH()
      })
    }).catch(err => {
      console.log(err)
    })
  },
  async getBevHisList() { //获取浏览历史 列表
    let arr = [];
    home.getBevHis('BOSS').then(res => {
      // console.log('res', res)
      if (res.length > 0) {
        arr = res.map(item => {
          item['create_time'] = formatDate(item.create_time, 'MM-dd')
          return item
        })
        arr = arr.slice(0, 10)
      }
      this.setData({
        browsingHistory: arr
      }, () => {
        this.scrollH()
      })
    }).catch(err => {
      console.log(err)
    })
  },
  //获取小米列表
  initGetList(callback) {
    const that = this;
    let {
      bazaarlist,
      bazaarHasData,
      bazaarParms
    } = that.data;
    that.setData({
      bazaarIsNull: false,
      bazaarIsFlag: false,
      bazaarHasData: true
    });
    // 计算卡片高度
    this.cardHeight()
    let parasm = {
      ...bazaarParms,
      kw: bazaarParms.ent_name?.trim()
    }
    delete parasm['ent_name']
    //
    boss.bossInfo(parasm).then(res => {
      let {
        datalist: items,
        count
      } = res
      let ary = []
      if (items.length < bazaarParms.page_size) bazaarHasData = false;
      ary = items.map((item, index) => {
        item.heightKey = bazaarParms['ent_name']
        item.myName = item.name
        return item;
      })
      ary = handleSearchHight(ary, 'myName', that.data.ent_name)
      that.setData({
        bazaarlist: bazaarlist.concat(ary),
        bazaarHasData,
        bazaarIsFlag: true,
        count: count || 0,
      }, () => {
        if (!that.data.bazaarlist.length) that.setData({
          bazaarIsNull: true,
          bazaarHasData: true
        });
      })
      callback && callback()
    }).catch(err => {
      callback && callback()
      app.showToast('获取数据失败!请稍后再试')
      console.log(err)
    })
  },
  // 下拉刷新
  bazaarRefresher() {
    const that = this;
    app.showLoading('加载中');
    wx.showNavigationBarLoading();
    let {
      bazaarParms
    } = that.data
    let obj = {
      ...bazaarParms,
      page_index: 1,
      page_size: 10
    }
    that.setData({
      bazaarParms: obj,
      bazaarlist: [],
      bazaarHasData: true,
    }, () => that.initGetList(() => {
      wx.hideLoading();
      that.setData({
        bazaarIsTriggered: false
      })
      wx.hideNavigationBarLoading()
    }))
  },
  //加载更多
  async bazaarloadMore() {
    let {
      bazaarParms,
      bazaarHasData,
      bazaarIsFlag
    } = this.data;
    if (!bazaarHasData) return;
    if (!bazaarIsFlag) return; //节流
    bazaarParms.page_index += 1;
    this.setData({
      bazaarParms
    }, () => this.initGetList());
  },
  //----------
  // 动态获取页面高度 
  cardHeight() {
    var that = this;
    getHeight(that, ['.tip'], (data) => {
      const {
        screeHeight,
        res
      } = data
      let h1 = res[0]?.top
      let h2 = res[0]?.height
      // console.log('res', screeHeight - h1 - h2)
      this.setData({
        cardHeight: screeHeight - h1 - h2
      })
    })
  },
  scrollH() {
    var that = this;
    getHeight(that, ['.searchs', '.search_a', '.drop-menu', '.his_title'], (data) => {
      const {
        screeHeight,
        res,
        statusBarHeight
      } = data
      let h1 = res[0]?.height || 0
      let h2 = res[1]?.height || 0
      let h4 = res[3]?.height || 0
      // 处理search外剩余的高度 
      let filtrateHeight = screeHeight - h1
      // 浏览历史的滚动高度 
      let scrollHeight = screeHeight - h1 - h2 - h4
      // console.log(scrollHeight, screeHeight, h1, h2, h4)
      that.setData({
        scrollHeight: scrollHeight,
        filtrateHeight: filtrateHeight,
        statusBarHeight: statusBarHeight,
      })
    })
  },
  handleTit({
    detail
  }) {
    const {
      isLogin
    } = this.data;
    this.setData({
      entUnlogin: !isLogin
    });
    if (isLogin) {
      let {
        name: enterprise_name,
        person_id: enterprise_id,
      } = detail
      let logo = detail?.pic || detail?.person_pic
      home.addBevHis({
        enterprise_name,
        enterprise_id,
        behavior_history_mode: 'INDEX_PAGE',
        enterprise_log: logo ? logo : "-",
        model_type: 'BOSS'
      }) //新增浏览历史 
    }
  },
  login() {
    const url = '/companyPackage/pages/searchs/searchs';
    app.route(this, `/pages/login/login?url=${url}`);
  },
  goDetail(e) {
    let {
      item: {
        enterprise_id: person_id
      }
    } = e.currentTarget.dataset;
    if (!person_id || person_id == 'e0') return;
    const url = encodeURIComponent(`https://reporth5.handidit.com/bossEnter?personid=${person_id}&isShowtupu=true&type=ChanYeTong`)
    // const url = encodeURIComponent(`http://192.168.110.179:8080/bossEnter?personid=${person_id}&isShowtupu=true&type=ChanYeTong`)
    // const url = encodeURIComponent(`http://j-h5-report.ihdwork.com/bossEnter?personid=${person_id}&isShowtupu=true&type=ChanYeTong`)
    app.route(this, `/subPackage/pages/webs/index?url=${url}`)
  },
});