<import src="/subPackage/template/wxParse/wxParse"></import>
<!--  style="padding-bottom: {{isIphoneX?'168rpx':'100rpx'}};" -->
<view class="artics">
  <!--  文章  -->
  <view class="artic_cont">
    <!--头部 -->
    <view style="padding:0 32rpx;">
      <view class="artic_head_t">{{item.title}}</view>
      <view class="artic_head_c">
        <view class="artic-subt">
          <view class="one">发文机关：{{item.issuing_authority_name}}</view>
          <view class="two">{{item.create_time}}</view>
        </view>
        <view class="three">政策类型：{{item.type_name}}</view>
        <view class="tags" wx:if="{{item.tag_name.length}}">
          <block wx:for="{{item.tag_name}}">
            <view class="{{'tag'+index}}">{{item}}</view>
          </block>
        </view>
      </view>
      <!-- tab切换兰 -->
      <view class="tabs">
        <block wx:for="{{tab}}">
          <view class="item {{curCode==item.code&&'active'}}" bindtap="checkTab" data-code="{{item.code}}">
            {{item.name}}
            <view class="line"></view>
          </view>
        </block>
      </view>
    </view>
    <!-- 文章和图片--这里具体怎么渲染（富文本） 到时候看 -->
    <view class="artic">
      <view class="par" wx:if="{{curCode=='a'}}">
        <template is="wxParse" data="{{wxParseData:courseDetailOne.nodes}}" />
      </view>
      <view class="par" wx:if="{{curCode=='b'}}">
        <template is="wxParse" data="{{wxParseData:courseDetailTwo.nodes}}" />
      </view>
    </view>

    <!-- 下载 -->
    <view class="pagenext" wx:if="{{ item.accessory.length}}">
      <block wx:for="{{item.accessory}}" wx:for-item="items">
        <view bindtap="openExcel" class="wj" wx:if="{{items}}" data-link="{{items}}" data-name="{{item.accessory_name[index]}}">
          <view class="left"><text style="color:#20263A;">附件{{index+1}}：</text><text>{{item.accessory_name[index]}}</text></view>
          <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/subPackage/image/other/dwon.png" class="img"></image>
        </view>
      </block>
    </view>
    <!-- 最新资讯 -->
    <view class="artic_about" wx:if="{{false}}">
      <view class="title">相关企业</view>
      <view class="cont">
        <!-- 最后一个padding-bottom:0 -->
        <view class="list">比亚迪股份有限公司</view>
        <view class="list" style="padding-bottom: 0;">比亚迪科技股份有限公司</view>
      </view>
    </view>
    <!-- 最新政策 -->
    <view class="artic_about" wx:if="{{aboutList.length>0}}" id="news">
      <view class="title">最新政策</view>
      <view class="conts">
        <block wx:for="{{aboutList}}" wx:key="index" wx:for-item="objs">
          <navigator hover-class="none" url="/subPackage/pages/policyDetail/policy?id={{objs.id}}">
            <view class="artic_about_card" style="{{index==(aboutList.length-1)&&'border: none;'}}">
              <view class="artic_about_card_l">
                <view class="titles" style="max-width:{{objs.img?' 534rpx':'660rpx'}} ;">
                  {{objs.title}}
                </view>
                <view class="textbtm">
                  <view class="time">发文机关：{{objs.issuing_authority_name}}</view>
                  <view>{{objs.create_time}}</view>
                </view>
              </view>
              <!-- <view class="artic_about_card_r flex_all_center" wx:if="{{objs.images}}">
                <image src="{{objs.img}}" mode="aspectFill" />
              </view> -->
            </view>
          </navigator>
        </block>
      </view>
    </view>
  </view>
  <!-- 底部-fiexd -->
  <view class="artic_foot" style="height: {{isIphoneX?'168rpx':'100rpx'}};" wx:if="{{false}}">
    <view class="artic_foot_wrap">
      <!-- <view class="artic_foot_item" bindtap='onClick' data-item='praise'>
        <view class="artic_foot_item_t  {{item.thumbs ? 'zan-active' :'zan'}}"></view>
        <view class="artic_foot_item_b">点赞</view>
      </view> -->
      <view class="artic_foot_wrap-l">
        <view class="artic_foot_item" bindtap='onClick' data-item='collect'>
          <view class="artic_foot_item_t {{item.collect ? 'wuxin-active' :'wuxin'}}"></view>
          <view class="artic_foot_item_b">收藏</view>
        </view>
        <view class="artic_foot_item zhuanfa">
          <!-- 绝对定位 为了不影响下面内容样式 触发事件依然有效 -->
          <button open-type="share" class="btn">
          </button>
          <view class="artic_foot_item_t fenxiang"></view>
          <view class="artic_foot_item_b">
            转发
          </view>
        </view>
      </view>
      <view class="artic_foot_about" bindtap='onClick' data-item='news'>
        最新政策
      </view>
    </view>
  </view>
</view>