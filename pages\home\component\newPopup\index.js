// pages/home/<USER>/newPopup/index.js
Component({
    properties: {

    },
    data: {

    },
    methods: {
        handleClick(e) {
            const type = e.currentTarget.dataset?.type || false //点击蒙层--预留
            // console.log(type)
            if (type) {
            } else {
                this.triggerEvent('callback')
            }
        },
        close() {
            this.triggerEvent('callback', true)
        }
    }
})
