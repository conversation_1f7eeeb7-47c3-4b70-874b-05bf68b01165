@import "/template/menuhead/index.scss";

/* 导航样式 */
.weui-navbar {
  position: relative;
  height: 96rpx;
  display: flex;
  align-content: flex-start;
  width: 100%;
  font-size: 28rpx;
  border-bottom: 1rpx solid #EEEEEE;
  background: #fff;
}

.weui-navbar__item {
  position: relative;
  display: flex;
  flex: 1;
  text-align: center;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #74798C;
  justify-content: center;
  align-items: center;

}

.weui-navbar__item.weui-bar__item_on {
  font-weight: 600;
  color: #E72410;
}

.weui-navbar__slider {
  height: 96rpx;
}

.weui-navbar__slider::after {
  position: absolute;
  content: "";
  width: 58rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #E72410 0%, #F17B6F 100%);
  border-radius: 8rpx;
  left: 50%;
  bottom: 0rpx;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}

.sci_heads .nav {
  background: #fff !important;
}

/* 弹窗 */
.container_hd {
  width: 100%;
  height: 100%;
  position: absolute;
  overflow-y: scroll;
  background-color: rgba(0, 0, 0, 0.5);
}

.disappear {
  display: none;
}

.show {
  display: block;
}

.z-height {
  overflow-y: scroll;
  background: #fff;
  border-radius: 0rpx 0rpx 16rpx 16rpx;
  max-height: 600rpx;

}


.sort_list {
  height: 96rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #20263A;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sort_list image {
  width: 32rpx;
  height: 32rpx;
}

.sort_list:not(:last-of-type) {
  border-bottom: 2rpx solid #eee;
}

.sort_list.active {
  color: #E72410;
  font-weight: 600;
  font-size: 28rpx;
}

/* 覆盖导航样式 */
.nav-title {
  display: inline-block;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263A;
}

.active .nav-title {
  color: #E72410;
}

.sci_heads {
  position: relative;
  z-index: 80;
}

.sci_heads .nav {
  background: #f7f7f7 !important;
  /* border: none !important; */
}

.active .nav-title {
  color: #E72410
}

/* 列表样式 */
.container {
  position: relative;
  z-index: 4;
  font-size: 14px;
}