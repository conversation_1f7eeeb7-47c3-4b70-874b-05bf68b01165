// source: {
//     key: 'ceshi', //返回出来的字段键
//     initVal: '1',//默认值
//     value: [
//         { name: '考察调研中', code: '1' },
//         { name: '落地选址中', code: '2' },
//         { name: '切换地址中', code: '3' },
//         { name: '恰谈落地中', code: '4' },
//     ]
// },
{
  /* <btm-list-pop title="项目阶段" options="{{source}}" visible="{{visiblePop}}" bindget="popcallback"
      initVal="{{form.ceshi}}"/> */
}
import {
  user,
  mine
} from '../../../service/api'
const app = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 控制组件显示/隐藏
    visible: {
      type: <PERSON><PERSON><PERSON>,
      observer: async function (val) {
        wx.hideTabBar()
        this.setData({
          isVisible: val
        })
      }
    },
    // 标题名称
    title: {
      type: String,
      value: '团队切换'
    },
    // 列表
    options: {
      type: Object,
      value: {},
      observer(val) {
        this.setData({
          dataOptions: val
        })
      }
    },
    initVal: {
      type: String,
    },
    list: {
      type: Array,
      observer(val) {
        if (!val.length) return;
        let arr = val.filter(i => i.org_id == this.data.initVal)
        if (arr != -1) {
          this.setData({
            curObj: {
              org_name: arr[0]?.org_name,
              org_code: arr[0]?.org_code,
              org_id: arr[0]?.org_id
            }
          })
        }
        this.setData({
          dataList: val
        })
      }
    }
  },
  data: {
    dataList: [],
    isVisible: false,
    curObj: {}
  },
  methods: {
    getItem(e) {
      var index, item, obj;
      index = e.currentTarget.dataset.index
      item = e.currentTarget.dataset.item
      obj = {
        org_name: item.org_name,
        org_code: item.org_code,
        org_id: item.org_id
      }
      this.setData({
        curObj: obj
      })
    },
    onclose() {
      this.triggerEvent('submit', false)
      wx.showTabBar()
    },
    async onsubmit() {
      try {
        let obj = this.data.curObj;
        if (this.data.initVal == this.data.curObj.org_id) {
          this.triggerEvent('submit', false)
          wx.showTabBar()
          return
        }
        let res0 = await user.tokenChange({
          org_code: obj.org_code
        })
        wx.removeStorageSync('TOKEN')
        wx.setStorageSync('TOKEN', res0)
        let res = await mine.getMyData()
        wx.removeStorageSync('userData')
        wx.setStorageSync('userData', res)
        this.triggerEvent('submit', obj)
        wx.showTabBar()
        let pages = getCurrentPages();
        if (pages.length > 0) {
          let len = pages.length - 1
          pages[len].setData({ // 针对我的做单独处理
            isFlag: true
          }, () => {
            pages[len]?.onLoad()
            pages[len]?.onShow()
            // 处理check.js混入
            pages[len]?.handleLoginStatus && pages[len]?.handleLoginStatus()
          })
        }
      } catch (err) {
        if (err) {
          app.showToast('切换团队失败!', 'none', 1500, false)
          this.triggerEvent('submit', false)
          wx.showTabBar()
          return
        }
      }

    }
  },
})