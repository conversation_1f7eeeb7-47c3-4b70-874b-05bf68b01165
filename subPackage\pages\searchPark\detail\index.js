// subPackage/pages/searchPark/detail/index.js
import {filterItem} from '../component/dropDownMenu/filterItem';
import {home} from '../../../../service/api';
import {getShareUrl, handleShareUrl} from '../../../../utils/mixin/pageShare';
const app = getApp();
Page({
  /**
   * 页面的初始数据
   */
  data: {
    filterItem,
    activeIndex: 0,
    erweicode_information: [
      // { erweicode:"./longsheng.jpg"},
      // { erweicode:"./longsheng.jpg"}
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 如果没有登录直接去登录页面 但是下面代码不能阻断不然后面返回就没数据了。
    console.log(JSON.parse(decodeURIComponent(options.data)));
    handleShareUrl();
    let oldData = options.data;
    let parkObj = JSON.parse(decodeURIComponent(options.data));
    if (parkObj) {
      this.setData({
        parkObj,
        oldData
      });
    }
  },
  onShow() {
    const islogin = app.isLogin();
    if (!islogin) {
      app.showToast('查看园区详情请先登录!');
      setTimeout(() => {
        app.route(this, `/pages/login/login`);
      }, 2000);
    }
    islogin && this.getParkEnt(this.data.parkObj);
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    console.log(
      '分享出去的：',
      `/subPackage/pages/searchPark/detail/index?data=${this.data.oldData}`
    );
    return {
      title: `邀请你查看${this.data.parkObj.parkName}信息`, //自定义转发标题
      path: getShareUrl(
        `/subPackage/pages/searchPark/detail/index?data=${this.data.oldData}`
      ) //分享页面路径
    };
  },
  //获取园区内企业
  getParkEnt(obj) {
    let data = {
      park_name: obj.parkName,
      region_code: obj.region
    };
    home.parkEntList(data).then(res => {
      console.log(res);
      if (res.count) {
        this.setData({
          entNum: res.count
        });
      }
    });
  },
  changeswiper(e) {
    var index = e.detail.current; //当前所在页面的 index
    this.setData({
      activeIndex: index
    });
  },
  // 跳转到入园企业列表
  async jumpEntList(e) {
    if (!this.data.entNum) {
      return;
    }
    let data = {
      park_name: this.data.parkObj.parkName,
      region_code: this.data.parkObj.region
    };
    let url = `../parkEntList/index?obj=${encodeURIComponent(
      JSON.stringify(data)
    )}`;
    app.route(this, url);
  },
  openMap(e) {
    console.log(e.currentTarget.dataset.item);
    if (e.currentTarget.dataset.item) {
      let adress = e.currentTarget.dataset.item;
      wx.getLocation({
        //定位类型 wgs84, gcj02
        type: 'gcj02',
        success: function (res) {
          console.log('定位信息', res);
          var url =
            'https://apis.map.qq.com/ws/geocoder/v1/?address=' +
            adress +
            '&key=EUPBZ-6MD3K-I6OJB-ALA4P-DZGD3-54F67';
          wx.request({
            url: url,
            success: function (res) {
              if (!res.data.result?.location) {
                wx.showToast({
                  title: '该地址不存在',
                  icon: 'error',
                  duration: 2000
                });
                return;
              }
              var location = res.data.result.location;
              wx.openLocation({
                address: adress,
                name: adress,
                longitude: +location.lng,
                latitude: +location.lat,
                scale: 18
              });
            }
          });
        }
      });
    }
  }
});
