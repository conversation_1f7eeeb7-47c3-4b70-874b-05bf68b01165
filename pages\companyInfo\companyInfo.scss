@import '/template/more/more.scss';
@import '/template/null/null.scss';



.page_conpanyInfo {
  position: relative;
  height: 100vh;
  overflow: hidden;
  position: relative;
  background-color: #fff;
}

.page_conpany_nav {
  height: 96rpx !important;
}

.page_conpanyInfo::after {
  content: " ";
  width: 100%;
  height: 1px;
  background: #eee;
  position: absolute;
  top: 0;
  transform: scaleY(0.5);
}

.content_page {
  background-color: #f7f7f7;
  padding: 1rpx 0;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.no_data {
  color: #74798c;
  text-align: center;
  padding-top: 80rpx;
}

.add_btn {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(90deg, #F56E60 0%, #E72410 100%);
  box-shadow: 0px 4rpx 4rpx 2rpx rgba(16, 19, 29, 0.1);
  border-radius: 48rpx;
  color: #fff;
  display: flex;
  position: absolute;
  right: 32rpx;
  bottom: 120rpx;
  font-size: 24rpx;
  padding: 0 26rpx;
  align-items: center;
}

.item_box {
  background-color: #fff;
  padding: 32rpx 24rpx;
}

.item_box:not(:last-child) {
  margin-top: 20rpx;
}

.item_box .title_box {
  height: 60rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.item_box .daily-title {
  display: flex;
  justify-content: space-between;
  font-size: 32rpx;
  color: #20263a;
}

.item_box .daily-title .time {
  color: #9b9eac;
  font-size: 28rpx;
}

.item_box .describe {
  height: 80rpx;
  background: rgba(74, 184, 255, 0.04);
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  border: 2rpx solid rgba(74, 184, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-top: 24rpx;
  margin-bottom: 8rpx;
}

.item_box .describe view {
  font-weight: 600;
  color: #1E75DB;
  margin: 0 10rpx;
}

.item_box .company_list {
  min-height: 60rpx;
  display: flex;
  justify-content: space-between;
  color: #74798c;
  font-size: 28rpx;
  margin-top: 24rpx;
}

.item_box .company_list .company_name {
  flex: 1;
  margin: 0 24rpx;
  line-height: 60rpx;
  color: #74798C;
}

.item_box .company_list .num {
  line-height: 60rpx;
  color: #20263A;
}

.item_box .company_list .logo,
.title_box .logo {
  width: 60rpx;
  height: 60rpx;
  border-radius: 2rpx;
  overflow: hidden;
  box-shadow: 0px 0px 6rpx #ddd;
  padding: 10rpx;
}

.item_box .company_list .logo image,
.title_box .logo image {
  width: 40rpx;
  height: 40rpx;
}

.openall {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  border-top: 1px solid #eee;
  margin-top: 24rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #1E75DB;
}

.title_box .title {
  flex: 1;
  margin: 0 24rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
  font-weight: 600;
  color: #20263A;
  font-size: 32rpx;
}

.title_box .btn {
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAABBJJREFUWEftmEFoHFUYx//f7jYUm8MWtHjIIZCIPSj1IIhUJJhmZ00rWJQadibdtwcxB6VIBQsijuChQg85GLRYyGyzk1ZRLBhlM1uwPRki4kUxhYoVN7XeKiLUZHc+me1unO7OvHkzITYH97bMe//vN9977/t/bwjb/Efbhe/sucXHMpy+Vigc+N3PlBjQNM3M4IP7NbicB/AIgL0A7m2JM98AUR3AUgq00FyrXy6VSrfCkmFVnP0gVAGu91F6xA8ZG3Bm5uP+/uzu48zuFIjuV1oB5htEdNJdWz3dDfovHPpva/GKHzIW4Fm7prlwzwA0oATWM4jrSDdHxMT4T96jXrj2BMbLwsjNeP+UAa352htgficZWCcwTwtDe1UGR2CzqGtvd+IoAZbt2lsMNv9rOKUMlm3nGAPTdwMuEnDufHVfs5laArAzMSDHX1blMmPZzjcAHt1KODB/Lwzt4bAYoXvQOlc7CJcXthSuLZ4ijBwt5C4HxQoHtBc/B+hQJCDjJhFPM/Ene7Lu1eXl5ebQ0JNDk5NPXZGWEr8w84IwtGeUAWdnP8tS367fIvcec/WevtTEkSNjfwSJh9a53sENXvvrvlLp8M3uR4EZvF2QuSrNHuMSr68+HWZhMeBaYVKg/FF9bFEJ0Ko4x0E4FQrIuLWD0nt1ffSXTWZuYzoBJ4p67l01QNt5H8CUBNASRq4UC465DpJa5HtCz72iCjgLQIQefWZRNLRy9/OwZfXsC4xrTGRJto0l9N6XDtyDVkQGg8qCDM7zVqvijIJwUQJ4Rui5F5UyGOm9Lh0Sk2NfdMSi4FrlZq52EKnwutrdJHS0AzNYrlx8gck9r7LEKnAtwKhuyHWfF5P5T5Uy+GHly4EdlPk1EDCht1q281278w6UpUZ6oFgcXVUCbL1xkA8nhCvPOy8x4wNJVVgSRu5xZSfxBpYri8U7Tl1CuHbRvyBzJSJMFQu507EATfOrzODw2o8gGkYCOM8uU339xxh8QmqZzHVev/5AmCNJO2prvvYcXPeJqDYdQAOMCyBeIUaGQcMADoCQldqld+dgniga2kehBzJKIKqUqM4PPnCuJYx8oCNJy4yqQ2wOjqu8fv2w7L7s6UdemuJ2JUrQrAYXCbgFcA1iPvXz1a/fNE2zofIy4R31xueIzo2/Lcc8DcIgQM+qBNgYw7gE4teErn0bZ15wsxAC5/fLubnqPpdogr1rAeGhgKANMK+0vrkQW6KQ/yEOmNyLbed1Bk76BcPM3Bvjfa/Zlc3+6R+/Z3dj5/j4+N9JoO6MG6Lg72hkcBtlyHbYLyX0XOQBVIGXiniQgAv/t5IwUetuAPbUwy4IlQwEjYmT3VjL0J2l/wFVnMSfpW2fwaRLupl5sfbgZgIlnfsPVOUhR2YBpF0AAAAASUVORK5CYII=");
  background-repeat: no-repeat;
  background-size: 40rpx 40rpx;
  cursor: pointer;
}

.risk_box {
  background: rgba(74, 184, 255, 0.04);
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  border: 2rpx solid rgba(74, 184, 255, 0.1);
  display: flex;
}

.risk_box .title {
  font-size: 24rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  writing-mode: tb-rl;
  letter-spacing: 5px;
  border-right: 2rpx solid #eee;
  color: #74798C;
}

.risk_box .content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
}

.risk_box .content .risk_item_box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.risk_box .content .risk_item_box:nth-child(1) {
  color: #E72410;
}

.risk_box .content .risk_item_box:nth-child(2) {
  color: #FFB93E;
}

.risk_box .content .risk_item_box:nth-child(3) {
  color: #FD7900;
}

.risk_box .content .risk_item_box:nth-child(4) {
  color: #4AB8FF;
}

.risk_box .content .risk_item_box:nth-child(5) {
  color: #26C8A7;
}

.risk_item_text {
  font-size: 20rpx;
  color: #8e8e8e;
  text-align: center;
}

.risk_box .content .name {
  color: #20263a;
  font-size: 24rpx;
  margin-bottom: 10rpx;
}

.risk_box .content .value {
  font-size: 36rpx;
  font-weight: 600;
}

.add_monitoring_page {
  background-color: #fff;
  padding-top: 24rpx;
}

.risk_boxss {
  background: rgba(74, 184, 255, 0.04);
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  border: 2rpx solid rgba(74, 184, 255, 0.1);
  display: flex;
  margin-top: 8rpx;
}

.risk_boxss .title {
  font-size: 24rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  writing-mode: tb-rl;
  letter-spacing: 5px;
  border-right: 2rpx solid #eee;
  color: #74798C;
}

.risk_boxss .content {
  display: flex;
  width: 100%;
}

.risk_boxss .risk_item_box {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
}

.risk_boxss .risk_item_box .name {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
}

.risk_boxss .risk_item_box .value {
  font-size: 36rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #E72410;
  margin-left: 8rpx;
}


/* input */
.searchs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0rpx 24rpx;
  background: #eeeeee;
  border-radius: 8rpx;
  padding: 16rpx 0 16rpx 28rpx;
}

.s-input {
  display: flex;
  align-items: center;
  flex: 1;
}

.s-input-img {
  width: 40rpx;
  height: 40rpx;
}

input {
  caret-color: #E72410;
  color: #74798c;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
}

.s-input-item {
  position: relative;
  flex: 1;
  height: 40rpx;
  padding-left: 16rpx;
}

.s-input-item-i {
  position: relative;
  width: 100%;
  height: 40rpx;
}

.placeholder {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
  line-height: 40rpx;
}

.search_items_box {
  background-color: #fff;
  padding: 24rpx;
}

.search_items_box .search_item {
  margin-top: 20rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 28rpx;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search_items_box .search_item.active {
  color: #E72410;
}

.search-btn-box {
  height: 180rpx;
  background-color: #fff;
  display: flex;
  position: fixed;
  width: 100%;
  bottom: 0;
  padding: 20rpx 24rpx;
  justify-content: space-between;
  border-top: 1px solid #eee;
}

.search-btn-box .search-btn {
  width: 340rpx;
  height: 80rpx;
  background-color: #f7f7f7;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 34rpx;
  color: #74798c;
  font-weight: 600;
}

.search-btn-box .search-btn.primary {
  background-color: #E72410;
  color: #fff;
}

.filter_box {
  height: 96rpx;
  background: #FFFFFF;
  display: flex;
  justify-content: start;
  align-items: center;
  padding-left: 24rpx;
}

.filter_title {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 36rpx;
}

.filter_item {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  padding: 12rpx 24rpx;
  background: #F7F7F7;
  border-radius: 4rpx;
  margin-right: 24rpx;
  height: 56rpx;
}

.tag_on {
  background: linear-gradient(90deg, #F56E60 0%, #E72410 100%);
  color: #fff;
}

.weui-navbar .weui-navbar__slider::after {
  width: 68rpx;
  height: 4rpx;
  background: #E72410;
  border-radius: 0;
  bottom: 0rpx;
}

/* 登录缺省页面 */
.card-login {
  position: relative;
  display: flex;
  flex-direction: column;
  /* justify-content: center; */
  align-items: center;
  width: 100%;
  height: 100vh;
  background: #f7f7f7;
}

.card-login image {
  width: 230rpx;
  height: 230rpx;
  margin-top: 204rpx;
}

.card-login .txt {
  padding-top: 40rpx;
  padding-bottom: 48rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  line-height: 33rpx;
}