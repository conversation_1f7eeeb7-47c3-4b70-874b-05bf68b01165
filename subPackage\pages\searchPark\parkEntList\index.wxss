@import '/template/more/more.wxss';
@import '/template/null/null.wxss';
@import '/template/loading/index.wxss';

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.page {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: #F2F2F2;
}

.sci_text {
  height: 96rpx;
  line-height: 96rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  padding-left: 32rpx;
  width: 100%;
  background: #fff;
}

.sci_text>text {
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #E72410;
}

.sci_line {
  width: 0rpx;
  height: 0rpx;
}

.bus-card {
  height: 100%;
  width: 100%;
}


/* 联系方式弹窗 */
.contact_box {
  position: relative;
  width: 100%;
  height: 96rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
}

.contact_box::after {
  content: " ";
  width: 100%;
  height: 1px;
  background: #eee;
  position: absolute;
  bottom: 0;
  /* left: -50%; */
  transform: scaleY(0.5);
}

.contact_box:last-child::after {
  content: " ";
  width: 100%;
  height: 1px;
  background: transparent;
  position: absolute;
  bottom: 0;
  /* left: -50%; */
  transform: scaleY(0.5);
}

.contact_left {
  display: flex;
  align-items: end;
  padding: 28rpx 0;
}

.contact_left image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.contact_number {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #E72410;
}

.contact_right {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #74798c;
}


/* dialog文字样式 */
.dialog-con {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #74798c;
}

.dialog-con .map {
  position: relative;
  width: 100%;
  height: 112rpx;
  line-height: 112rpx;
  text-align: center;
  font-size: 34rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #E72410;
}

.weui-dialog__title {
  font-weight: 500 !important;
  color: #000000 !important;
  line-height: 40rpx;
  font-size: 34rpx !important;
}

.light_bd {
  padding: 0 !important;
}

.dialog-con .map::after {
  content: " ";
  width: 200%;
  height: 1rpx;
  background: #E5E5E5;
  position: absolute;
  top: 0;
  left: 0%;
  transform: scaleY(0.5);
}

.dialog-con .cancel {
  position: relative;
  height: 112rpx;
  line-height: 112rpx;
  text-align: center;
  font-size: 34rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
}

.dialog-con .cancel::after {
  content: " ";
  width: 200%;
  height: 1rpx;
  background: #E5E5E5;
  position: absolute;
  top: 0;
  left: 0%;
  transform: scaleY(0.5);
}