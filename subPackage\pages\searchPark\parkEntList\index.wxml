<import src="/template/null/null"></import>
<import src="/template/more/more"></import>
<import src="/template/loading/index"></import>

<view class="page">
  <!-- 筛选条件 -->
  <view class="sci_text">
    为您找到 <text>{{count}}</text> 家入园企业
  </view>
  <view class="sci_line"></view>
  <block>
    <!-- 卡片!bazaarIsNull -->
    <view wx:if="{{!bazaarIsNull}}">
      <scroll-view bindrefresherrefresh="bazaarRefresher" refresher-triggered="{{bazaarIsTriggered}}" refresher-enabled bindscrolltolower="bazaarloadMore" scroll-y style="height: {{cardHeight}}px;" class="sci_scroll">
        <view class="bus-card">
          <block>
            <block wx:if="{{bazaarlist.length>0}}">
              <block wx:for="{{bazaarlist}}" wx:key="index">
                <Card bindcardFun='onCard' obj="{{item}}" bindtap="goto" data-item="{{item}}" />
              </block>
            </block>
            <view wx:else style="height: {{cardHeight}}px;">
              <template is="load"></template>
            </view>
          </block>
          <view wx:if="{{bazaarlist.length>=bazaarParms.page_size}}" style="width: 100%;">
            <template is='more' data="{{hasData:bazaarHasData}}"></template>
          </view>
        </view>
      </scroll-view>
    </view>
    <!--暂无数据 bazaarIsNull-->
    <view wx:if="{{bazaarIsNull}}" style="width: 100%;height: {{cardHeight}}px;">
      <template is='null'></template>
    </view>
  </block>
</view>




<!-- 联系方式弹窗 -->
<dialog visible='{{showContact}}' title="联系方式" isShowConfirm='{{false}}' cancelBtnText="关闭" bindclose='onCloseContact'>
  <view class="dialog-con">
    <view class="contact_box" wx:for="{{contactList}}" wx:key="index">
      <view class="contact_left" bindtap="makeCall" data-item="{{item}}">
        <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"></image>
        <text class="contact_number">{{item.contact_data}}</text>
      </view>
      <view class="contact_right">{{item.sources[0]}}</view>
    </view>
  </view>
</dialog>

<!-- 地址弹窗 -->
<dialog visible='{{showAddress}}' title="地址" isShowConfirm='{{false}}' showFooter="{{false}}">
  <view class="dialog-con">
    <view style="padding: 0 50rpx;">
      <map id="map" longitude="{{location.lon}}" latitude="{{location.lat}}" markers="{{addmarkers}}" scale="{{11}}" style="width: 100%; height: 306rpx;">
      </map>
    </view>
    <view style="margin: 32rpx 0;font-size: 28rpx;">{{locationTxt}}</view>
    <!-- <view bindtap="goMap" class="map">
      导航
    </view>
    <view class="cancel" bindtap="onCloseAddress">
      取消
    </view> -->
  </view>
</dialog>