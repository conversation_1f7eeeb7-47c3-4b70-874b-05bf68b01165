.flex_all_center {
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes c {
    0% {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.fadeIn {
    -webkit-animation: c 0.3s forwards;
    animation: c 0.3s forwards;
}

.mask {
    background: rgba(0, 0, 0, 0.6);
}

.mask,
.mask1 {
    position: fixed;
    z-index: 299;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
}

.pop {
    position: fixed;
    z-index: 1000;
    top: 30%;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 604rpx;
    height: 664rpx;
    background: #FFFFFF;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    opacity: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.img-one {
    width: 604rpx;
    height: 396rpx;

}

.img-two {
    width: 360rpx;
    height: 62rpx;
    margin-top: -40rpx;
    margin-bottom: 20rpx;
}

.pop image {
    width: 100%;
    height: 100%;
}

.pop .wz {
    font-size: 40rpx;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #74798C;
    line-height: 47rpx;
    padding-bottom: 40rpx;
}

.pop .wz text {
    color: #FFB93E;
}

.pop .btn {
    width: 484rpx;
    height: 80rpx;
    background: linear-gradient(270deg, #076EE4 0%, #4AB8FF 100%);
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    font-size: 34rpx;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #FFFFFF;
    line-height: 80rpx;
    text-align: center;
}