<import src="/template/null/null"></import>
<import src="/template/more/more"></import>
<import src="/template/loading/index"></import>
<view  class="finpages"  bindtouchend="touchEnd" bindtouchmove="touchEnd">
  <!-- 头部 -->
  <view class="head">
    <view class="bg">
      <!-- 背景 -->
      <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/chayuanqu_bg.png" mode="aspectFill" class="head_bg"></image>
    </view>

    <!-- input框 -->
    <view class="input_box" style="background-color: #fff;width: 100%;padding-bottom: 28rpx;">
      <view class="ipt-box">
        <view class="sear"></view>
        <input type="text" placeholder="请输入园区名搜索" placeholder-class="pla" class="ipt" confirm-type='search' bindinput="inputChange" value="{{bazaarParms.parkName}}" />
      </view>
    </view>

  </view>
  <!-- 头部筛选框 -->
  <view class="searPop" wx:if="{{isSearPop}}">
    <view class="content">
      <block wx:for="{{headFilterData.list}}" wx:key="index">
        <view class="{{(headFilterData.curId==item.id)&&'active'}}" catchtap="clickHeadList" data-item="{{item}}">{{item.name}} <text wx:if="{{item.text}}" catchtouchstart="tooltip" data-item="{{item}}"></text></view>
      </block>
    </view>
  </view>
  <!-- 筛选条件 筛选框 -->
  <block>
    <DropDownMenu height="{{filtrateHeight}}" dropDownMenuTitle="{{dropDownMenuTitle}}" class="drop-menu" bindsubmit="onFlitter" isFocus="{{isFocus}}" regionTop="{{regionTop}}" />
    <view class="sci_text">
      共找到 <text>{{count}}</text> 个产业园
    </view>
    <view class="sci_line"></view>
  </block>
  <block>
    <!-- 卡片!bazaarIsNull -->
    <view wx:if="{{!bazaarIsNull}}">
      <scroll-view  bindrefresherrefresh="bazaarRefresher" bindscroll="isScrollUp" refresher-triggered="{{bazaarIsTriggered}}" refresher-enabled bindscrolltolower="bazaarloadMore" scroll-y style="height: {{cardHeight}}px;" class="sci_scroll" wx:if="{{flag}}">
        <view class="bus-card">
          <block>
            <block wx:if="{{bazaarlist.length>0}}">
              <block wx:for="{{bazaarlist}}" wx:key="index">
                <!-- 卡片 -->
                <Card obj="{{item}}" bindcardFun="collect" bindgoDetail="goDetail" searchKey="{{bazaarParms.parkName}}" data-item="{{index}}" />
              </block>
            </block>
            <view wx:else style="height: {{cardHeight}}px;">
              <template is="load"></template>
            </view>
          </block>
          <view wx:if="{{bazaarlist.length<count}}" style="width: 100%;">
            <template is='more' data="{{hasData:bazaarHasData}}"></template>
          </view>
        </view>
      </scroll-view>
    </view>
    <!-- vip弹窗 -->
    <!-- <VipPop visible="{{vipVisible}}"></VipPop>
    <view wx:if="{{!privileFlag}}" class="vip-box">
      <VipOccupancy bindsubmit="vipPop" />
    </view> -->
    <VipPop visible="{{vipVisible}}"></VipPop>
    <!-- 没有登陆 -->
    <!-- <view class="noLbtn" wx:if="{{showLoginPag}}">
      <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>" class="img"></image>
      <view class="btn">
        <LoginBtn isCard text="登录"></LoginBtn>
      </view>
    </view> -->

    <!-- 没有vip权限 -->
    <!-- <view wx:if="{{isLogin && !privileFlag}}" class='vipPop'>
      <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/vipPop.png" mode="aspectFill" />
      <view class="vip-button" bindtap="vipButtonClick">
      </view>
    </view> -->
    <!--暂无数据 bazaarIsNull-->
    <view wx:if="{{bazaarIsNull}}" style="width: 100%;height: {{cardHeight}}px;">
      <template is='null'></template>
    </view>
  </block>
</view>