@import '/template/more/more.scss';
@import '/template/null/null.scss';

.mynews {
    position: relative;
    background: #fff;
    width: 100%;
    height: 100vh;
    flex-direction: column;
    overflow: hidden;
}

.select-box {
    height: 96rpx;
    background-color: #fff;
    border-top: 1px solid #f5f5f5;
    border-bottom: 20rpx solid #f7f7f7;
}

::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
}


.mynews .wrap {
    padding-left: 24rpx;
    padding-right: 24rpx;
}

/* card */

.newsCard {
    position: relative;
    width: 100%;
    /* background: #ffffff; */
    border-radius: 8rpx;
    /* box-shadow: 0rpx 4rpx 18rpx 0rpx rgba(221, 221, 221, 0.5); */
    margin-top: 20rpx;
}

.newsCard .shaw {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.newsCard-t {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24rpx;
    height: 88rpx;
    /* border-bottom: #f7f7f7 1px solid; */
}

.newsCard-t::after {
    content: " ";
    /* width: 100%; */
    height: 1px;
    background: #eee;
    position: absolute;
    bottom: 0;
    left: 24rpx;
    right: 24rpx;
    transform: scaleY(0.5);
}

.newsCard-t view:nth-child(1) {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    color: #E72410;
}

.newsCard-t view:nth-child(2) {
    font-size: 24rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    color: #9b9eac;
}

/* 标题加内容布局 */
.newsCard-n {
    font-size: 32rpx;
    font-family: PingFang SC, PingFang SC-Semibold;
    font-weight: 600;
    text-align: LEFT;
    color: #20263a;
    padding: 32rpx 0 16rpx 32rpx;
}

.newsCard-cont {
    padding: 0 32rpx 32rpx;
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #74798c;
    border-bottom: 1px solid #f7f7f7;
}

/* 标题+内容  图文布局*/
.essay {
    position: relative;
    display: flex;
    padding: 24rpx 0;
    margin: 0 28rpx;
    /* border-bottom: 1px solid rgba(238, 238, 238, 1); */
}

.essay-img {
    width: 136rpx;
    height: 136rpx;
    margin-right: 20rpx;
    flex-shrink: 0;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    overflow: hidden;
}

.essay-c {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
}

.essay_title {
    font-size: 32rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #20263A;
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.essay_note {
    display: flex;
    flex-wrap: nowrap;
    font-size: 24rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #74798c;
    justify-content: space-between;
    margin-top: 20rpx;
    width: 100%;
}

.flex {
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
}

.flex .source {
    width: 360rpx;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}


.zhanwei {
    width: 20rpx;
}

/*  */




.newsCard-btm {
    position: relative;
    padding: 0 32rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.newsCard-btm::before {
    content: " ";
    height: 1px;
    background: #EEEEEE;
    position: absolute;
    top: 0;
    left: 32rpx;
    right: 32rpx;
    transform: scaleY(0.5);
}

.newsCard-btm view:nth-child(1) {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #74798c;
}

.newsCard-btm .img {
    width: 32rpx;
    height: 32rpx;
}

/* 缺省 */

.nulls {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 27rpx;
    color: #20263a;
    background-color: #f7f7f7;
}

.nulls .img {
    width: 404rpx;
    height: 204rpx;
    margin-top: 170rpx;
}

.nulls .text {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    color: #20263A;
    padding-top: 40rpx;
}