import constant from '../../utils/constant';
import {
  common
} from '../../service/api'
import {
  jump
} from '../../utils/util';
const app = getApp();
Component({
  properties: {},
  data: {
    currentIdx: 0,
    preIndex: 0,
    swiperError: 0,
    list: [],
    isShow: false,
    tempList: []
  },
  observers: {
    currentIdx(newVal) {
      // 未登录用户不埋点
      const isLogin = app.isLogin()
      if (!isLogin) return;
      let {
        list,
        tempList
      } = this.data
      let {
        id
      } = list[newVal]
      if (newVal == 0) return //第一个不处理
      // 切换了 就埋点 
      if (tempList.includes(id)) {
        return
      }
      tempList.push(id)
      this.eventTrack(id)
    }
  },
  methods: {
    async getInit() {
      let [err, res] = await app.to(common.swiper({
        ads_promote_type: 0
      }))
      if (err) {
        this.setData({
          list: [],
          isShow: false
        })
        return
      }
      wx.setStorageSync(constant.Swiper, 'swiper')
      // console.log(res)
      // 针对app大于3的广告进行筛选 
      let arr = res.length ? res.filter((i) => {
        if (i.ads_promote_link_type < 4) return i
      }) : [];
      this.setData({
        list: arr,
        isShow: arr.length
      })
    },
    onChange(detail) {
      if (detail.detail.source == "detail") {
        //当页面卡死的时候，current的值会变成0 
        if (detail.detail.current == 0) {
          //有时候这算是正常情况，所以暂定连续出现3次就是卡了
          let swiperError = this.data.swiperError
          swiperError += 1
          this.setData({
            swiperError: swiperError
          })
          if (swiperError >= 3) { //在开关被触发3次以上
            // console.error(this.data.swiperError)
            this.setData({
              currentIdx: this.data.preIndex
            }); //，重置current为正确索引
            this.setData({
              swiperError: 0
            })
          }
        } else { //正常轮播时，记录正确页码索引
          this.setData({
            preIndex: detail.detail.current
          });
          //将开关重置为0
          this.setData({
            swiperError: 0
          })
        }
      }
      // 这里还要埋点
      this.setData({
        currentIdx: detail.detail.current
      })
    },
    onGoto(e) {
      let {
        item
      } = e.currentTarget.dataset
      // 链接---这里要考虑是否有些页面需要传参数
      // app.route(this, ads_promote_link)
      this.setData({
        isShow: false
      })
      jump(this, app, item)
    },
    changeCurIdx(e) {
      this.setData({
        currentIdx: e.currentTarget.dataset.index
      })
    },
    close() {
      this.setData({
        isShow: false
      })
    },
    // 埋点 
    eventTrack(id) {
      common.eventTrack({
        event_type: 1,
        event_id: id
      })
    }
  },
  pageLifetimes: {
    show() {
      // 未登录用户不弹 
      const isLogin = app.isLogin()
      if (!isLogin) {
        this.setData({
          isShow: false
        })
        return
      };
      let swiper = wx.getStorageSync(constant.Swiper) || false; //弹了就不弹了 
      if (swiper) return;
      setTimeout(() => this.getInit(), 200)
    }
  },
  lifetimes: {
    attached() {

    }
  }
})