import { hijack } from '../../../../utils/route'
import { jump } from '../../../../utils/util'
const app = getApp();
Component({
  properties: {
    swiperList: {
      type: Array,
      value: []
    }
  },
  data: {
    currentIdx: 0,
    preIndex: 0,
    swiperError: 0
  },

  methods: {
    onChange(detail) {
      if (detail.detail.source == "detail") {
        //当页面卡死的时候，current的值会变成0 
        if (detail.detail.current == 0) {
          //有时候这算是正常情况，所以暂定连续出现3次就是卡了
          let swiperError = this.data.swiperError
          swiperError += 1
          this.setData({ swiperError: swiperError })
          if (swiperError >= 3) { //在开关被触发3次以上
            // console.error(this.data.swiperError)
            this.setData({ currentIdx: this.data.preIndex });//，重置current为正确索引
            this.setData({ swiperError: 0 })
          }
        } else {//正常轮播时，记录正确页码索引
          this.setData({ preIndex: detail.detail.current });
          //将开关重置为0
          this.setData({ swiperError: 0 })
        }
      }
    },
    onGoto: hijack(function (e) {
      let { item } = e.currentTarget.dataset
      jump(this, app, item)
    }, { type: 'swiper-btn', app: app })
  }
})