# SCSS 使用示例

本文档展示了如何在项目中使用 SCSS 的各种特性，包括变量、混合、嵌套等。

## 📁 文件结构

```
styles/
├── variables.scss      // 全局变量
├── mixins.scss        // 全局混合
├── base.scss          // 基础样式
└── components.scss    // 组件样式

pages/
└── example/
    ├── example.js
    ├── example.wxml
    ├── example.json
    └── example.scss   // 页面样式
```

## 🎨 使用全局变量和混合

### 1. 在页面样式中引入全局文件

```scss
// pages/example/example.scss
@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.page {
  background-color: $bg-color;
  padding: $spacing-lg;
  min-height: 100vh;
}
```

### 2. 使用变量

```scss
.header {
  background: $primary-color;
  color: $white;
  padding: $spacing-xl $spacing-lg;
  border-radius: $border-radius-lg;
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
}

.card {
  background: $white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-1;
  margin-bottom: $spacing-lg;
  
  .title {
    color: $text-color-primary;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
  }
  
  .content {
    color: $text-color-secondary;
    font-size: $font-size-md;
    line-height: $line-height-lg;
  }
}
```

### 3. 使用混合 (Mixins)

```scss
.button {
  @include gradient-button($gradient-primary);
  padding: $spacing-md $spacing-xl;
  font-size: $font-size-md;
  
  &.round {
    @include round-button(100rpx);
  }
  
  &:disabled {
    @include button-disabled;
  }
}

.search-input {
  @include input-base;
  margin-bottom: $spacing-lg;
  
  &.error {
    @include input-error;
  }
}

.modal {
  @include absolute-center;
  @include card-base;
  @include fade-in;
  width: 600rpx;
  padding: $spacing-3xl;
}
```

## 🔧 SCSS 高级特性示例

### 1. 嵌套和父选择器引用

```scss
.nav {
  background: $white;
  border-bottom: $border-width solid $border-color;
  
  .nav-item {
    padding: $spacing-lg;
    color: $text-color-secondary;
    transition: color $duration-base;
    
    &.active {
      color: $primary-color;
      font-weight: $font-weight-semibold;
    }
    
    &:hover {
      color: $primary-color;
    }
    
    &:last-child {
      border-right: none;
    }
  }
}
```

### 2. 条件语句和循环

```scss
// 生成不同尺寸的按钮
@each $size, $padding in (
  small: $spacing-sm $spacing-md,
  medium: $spacing-md $spacing-lg,
  large: $spacing-lg $spacing-xl
) {
  .button--#{$size} {
    padding: $padding;
  }
}

// 生成不同颜色的标签
$tag-colors: (
  primary: $primary-color,
  success: $success-color,
  warning: $warning-color,
  danger: $danger-color
);

@each $name, $color in $tag-colors {
  .tag--#{$name} {
    background: rgba($color, 0.1);
    color: $color;
    border: $border-width solid rgba($color, 0.2);
  }
}
```

### 3. 函数使用

```scss
// 计算函数
@function rem($px) {
  @return $px / 16 * 1rem;
}

// 颜色函数
.button {
  background: $primary-color;
  
  &:hover {
    background: darken($primary-color, 10%);
  }
  
  &:active {
    background: darken($primary-color, 20%);
  }
}

.alert {
  background: lighten($warning-color, 40%);
  border: $border-width solid $warning-color;
  color: darken($warning-color, 20%);
}
```

## 📱 响应式设计

```scss
.container {
  padding: $spacing-lg;
  
  @include media-sm {
    padding: $spacing-xl;
  }
  
  @include media-md {
    padding: $spacing-2xl;
    max-width: 1200rpx;
    margin: 0 auto;
  }
}

.grid {
  display: grid;
  gap: $spacing-lg;
  grid-template-columns: 1fr;
  
  @include media-sm {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @include media-lg {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

## 🎯 组件样式示例

### 卡片组件

```scss
.card {
  @include card-base;
  @include card-padding;
  @include card-hover;
  margin-bottom: $spacing-lg;
  
  .card-header {
    @include flex-between;
    margin-bottom: $spacing-md;
    padding-bottom: $spacing-md;
    @include hairline(bottom);
    
    .title {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
    }
    
    .extra {
      color: $text-color-secondary;
      font-size: $font-size-sm;
    }
  }
  
  .card-body {
    .description {
      @include text-ellipsis-multiline(3);
      color: $text-color-secondary;
      line-height: $line-height-lg;
      margin-bottom: $spacing-md;
    }
  }
  
  .card-footer {
    @include flex-between;
    margin-top: $spacing-md;
    padding-top: $spacing-md;
    @include hairline(top);
    
    .tags {
      @include flex-center-vertical;
      gap: $spacing-sm;
    }
    
    .actions {
      @include flex-center-vertical;
      gap: $spacing-md;
    }
  }
}
```

### 列表组件

```scss
.list {
  background: $white;
  border-radius: $border-radius-lg;
  overflow: hidden;
  
  .list-item {
    @include flex-between;
    padding: $spacing-lg;
    @include hairline(bottom);
    transition: background-color $duration-base;
    
    &:last-child::after {
      display: none;
    }
    
    &:active {
      background: $gray-1;
    }
    
    .item-content {
      flex: 1;
      
      .title {
        font-size: $font-size-md;
        color: $text-color-primary;
        margin-bottom: $spacing-xs;
      }
      
      .subtitle {
        font-size: $font-size-sm;
        color: $text-color-secondary;
      }
    }
    
    .item-extra {
      @include flex-center-vertical;
      color: $text-color-secondary;
      font-size: $font-size-sm;
      
      .arrow {
        margin-left: $spacing-sm;
        width: 12rpx;
        height: 12rpx;
        border-top: 2rpx solid currentColor;
        border-right: 2rpx solid currentColor;
        transform: rotate(45deg);
      }
    }
  }
}
```

## 🔄 动画效果

```scss
.fade-enter {
  @include fade-in;
}

.slide-up-enter {
  @include slide-in-up;
}

.scale-enter {
  @include scale-in;
}

// 自定义动画
.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
```

## 💡 最佳实践

### 1. 变量命名规范

```scss
// ✅ 好的命名
$primary-color: #E72410;
$text-color-primary: #323233;
$spacing-large: 24rpx;
$border-radius-medium: 12rpx;

// ❌ 不好的命名
$red: #E72410;
$dark: #323233;
$big: 24rpx;
$round: 12rpx;
```

### 2. 混合使用规范

```scss
// ✅ 参数化混合
@mixin button-size($padding-y, $padding-x, $font-size) {
  padding: $padding-y $padding-x;
  font-size: $font-size;
}

// ✅ 条件混合
@mixin button-variant($bg-color, $text-color: white) {
  background: $bg-color;
  color: $text-color;
  
  &:hover {
    background: darken($bg-color, 10%);
  }
}
```

### 3. 嵌套深度控制

```scss
// ✅ 合理的嵌套深度（不超过3层）
.card {
  .header {
    .title {
      font-weight: bold;
    }
  }
}

// ❌ 过深的嵌套
.page {
  .container {
    .section {
      .card {
        .header {
          .title {
            // 太深了！
          }
        }
      }
    }
  }
}
```

这些示例展示了如何在微信小程序项目中有效使用 SCSS 来编写更优雅、可维护的样式代码。
