@import '/template/null/null.scss';
.list {
    height: calc(100vh - 300rpx);
    width: 100%;
    padding-bottom: 19rpx;
    /* padding-top: 20rpx; */
  }
  .search-card {
    width: 702rpx;
    background: #ffffff;
    padding: 32rpx 24rpx 16rpx;
    /* height: 244rpx; */
    border-radius: 8rpx;
    box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(221,221,221,0.50); 
    margin-top: 20rpx;
    margin-bottom: 20rpx;
    margin-left: 24rpx;
    overflow: hidden;
  }
  .search-card .card-boxs {
    width: 100%;
    display: flex;
    align-self: start;
    justify-content: flex-start;
    margin-bottom: 44rpx;
  }
  .search-card .card-logo {
    width: 100rpx;
    height: 100rpx;
    flex-shrink: 0;
    box-shadow: 0rpx 0rpx 8rpx 2rpx rgba(32, 38, 58, 0.10000000149011612);
    border-radius: 8rpx;
    margin-right: 24rpx;
    overflow: hidden;
  }
  .card-head {
    width: 100%;
    overflow: hidden; 
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .card_h {
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    /* flex: 1; */
    /* margin-bottom: 18rpx; */
  }
  
  .card_h_l {
    width: 440rpx;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-size: 32rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #20263a;
  }
  .input_match{
    color:#E72410;
  }
  /* .card_h_l text {
    color:#E72410;
  } */
  
  .card_h_r {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .card_h_r_img {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36rpx;
    height: 36rpx;
  }
  .card_tag {
    display: flex;
    /* overflow: hidden; */
    width: 580rpx;
  }
  .card_tag_box text {
    margin-right: 16rpx;
    font-size: 24rpx;
    padding: 2rpx 12rpx 2rpx;
    border-radius: 4rpx;
  }
  
  .card_tag_i {
    display: inline-flex;
    padding: 4rpx 12rpx;
    min-width: 72rpx;
    margin-right: 16rpx;
    font-size: 24rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    justify-content: center;
    align-items: center;
    border-radius: 4rpx;
  }
  
  .card_c {
    display: flex;
    justify-content: space-around;
    padding: 20rpx 0 20rpx;
  }
  
  .card_c_i {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
  }
  .card_c_is::before {
    position: absolute;
    content: "";
    right: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    border-right: 1px solid rgba(238, 238, 238, 0.8);
    width: 0;
    height: 60rpx;
  }
  .card_c_i .name {
    font-size: 24rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: CENTER;
    color: #9b9eac;
    padding-bottom: 12rpx;
  }
  .card_c_i .cont {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: CENTER;
    color: #74798c;
  }
  .card_c_i .contblue {
    color: #E72410;
  }
  
  /* 那根横线 后面在调 */
  .card_ico {
    display: flex;
    justify-content: flex-end;
    flex-wrap: nowrap;
    /* margin: 44rpx 0 0 0; */
    position: relative;
    /* border-top: 1px solid #eee; */
  }
  .distance_txt{
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #9b9eac;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
  .card_ico::before{
    content: " ";
    width: 200%;
    height: 1rpx;
    background: #EEEEEE;
    position: absolute;
    top: -16rpx;
    left: -50%;
    transform: scaleY(0.5);
  }
  
  .card_ico_i {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8rpx;
    margin-right: 40rpx;
    padding: 8rpx 12rpx;
    font-size: 24rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #20263A;
    border: 1rpx solid #DEDEDE;
  }
  .card_ico_i:last-of-type{
    margin-right: 0;
  }
  .card_ico_i_img {
    width: 32rpx;
    height: 32rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 4rpx;
  }


  /* 企业标签样式 */
text.darkBlue {
  background: rgba(7, 110, 228, 0.1);
  color: #E72410;
}

text.wathetBlue {
  background: rgba(74, 184, 255, 0.1);
  color: #4ab8ff;
}

text.yellow {
  background: rgba(255, 185, 62, 0.12);
  color: #ffb93e;
}

text.green {
  background: rgba(38, 200, 167, 0.1);
  color: #26c8a7;
}

text.purple {
  background: rgba(156, 133, 219, 0.12);
  color: #9c85db;
}

text.brown {
  background: rgba(203, 155, 155, 0.1);
  color: #cb9b9b;
}

text.onBusiness {
  background: rgba(1, 144, 225, 0.1);
  color: #0190e1;
}

text.comColor {
  background: rgba(0, 173, 211, 0.1);
  color: #00add3;
}

text.miniCom {
  background: rgba(228, 113, 7, 0.1);
  color: #e47107;
}

text.logOff {
  background: rgba(92, 96, 112, 0.1);
  color: #7f8498;
}

/* 没有定义到的 */
text.customColor {
  background: rgba(243, 100, 32, 0.1);
  color: #f36420;
}
.login-tip {
  margin-top: -324rpx;
}
.empty-box {
  transition: height .3s;
}