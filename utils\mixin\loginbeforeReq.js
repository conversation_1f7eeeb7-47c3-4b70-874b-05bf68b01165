import {
  common
} from '../../service/api'
import constant from '../../utils/constant';

// 这个用于没有登录就需要调取的公共接口 -以前放到登录页面的 ，目前首页会掉 用到这个接口的页面有分享功能可能也需要调取
const commonRequest = async () => {
  // 获取本地缓存中的数据
  const eleseicAry = wx.getStorageSync(constant.EleseicAry)
  const allCertAry = wx.getStorageSync(constant.AllCertAry)
  const enttypeAry = wx.getStorageSync(constant.EnttypeAry)
  const districtAry = wx.getStorageSync(constant.DistrictAry)

  // 如果缓存中没有数据，则异步获取
  const promises = []
  if (!eleseicAry) {
    promises.push(common.eleseicPlus([]))
  }
  if (!allCertAry) {
    promises.push(common.getAllCert())
  }
  if (!enttypeAry) {
    promises.push(common.allEntType())
  }
  if (!districtAry) {
    promises.push(common.districtPlus([]))
  }
  // 等待所有异步操作完成后，更新本地缓存  
  try {
    const [eleseicRes, allCertRes, enttypeRes, districtRes] = await Promise.all(promises)
    if (eleseicRes) {
      wx.setStorageSync(constant.EleseicAry, JSON.stringify(eleseicRes))
    }
    if (allCertRes) {
      wx.setStorageSync(constant.AllCertAry, JSON.stringify(allCertRes))
    }
    if (enttypeRes) {
      wx.setStorageSync(constant.EnttypeAry, JSON.stringify(enttypeRes))
    }
    if (districtRes) {
      wx.setStorageSync(constant.DistrictAry, JSON.stringify(districtRes))
    }
  } catch (error) {
    console.log(error)
  }
}


module.exports = {
  commonRequest
}