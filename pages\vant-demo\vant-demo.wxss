/* pages/vant-demo/vant-demo.wxss */
.vant-demo {
  padding: 20rpx;
  background-color: #f7f8fa;
  min-height: 100vh;
}

.demo-section {
  margin-bottom: 40rpx;
}

.demo-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #323233;
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16rpx;
}

.demo-subtitle {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  border-left: 6rpx solid #1989fa;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 0 20rpx;
}

.tab-content {
  padding: 40rpx 20rpx;
  text-align: center;
  color: #646566;
  background-color: white;
  margin: 20rpx 0;
  border-radius: 12rpx;
}

.popup-content {
  padding: 40rpx;
  text-align: center;
  background-color: white;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #323233;
  margin-bottom: 20rpx;
}

.popup-text {
  font-size: 28rpx;
  color: #646566;
  margin-bottom: 40rpx;
  line-height: 1.6;
}

/* 覆盖 Vant 组件样式 */
.van-search {
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
}

.van-cell-group {
  border-radius: 12rpx;
  overflow: hidden;
}

.van-dropdown-menu {
  border-radius: 12rpx;
  overflow: hidden;
}

.van-button {
  border-radius: 12rpx !important;
}

/* 自定义主题色 */
.van-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.van-button--success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border: none;
}

.van-button--warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border: none;
}

.van-button--danger {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  border: none;
}
