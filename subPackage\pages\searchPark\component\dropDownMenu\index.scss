@import "/template/menuhead/index.scss";


.sci_head {
  position: relative;
  z-index: 100;
  /* border-bottom: 1px solid red; */
  border-bottom: 1px solid #EEEEEE;
}

.nav {
  position: relative;
  display: flex;
  border: none !important;
  background: #fff;
  height: 96rpx;
}

.active .nav-title {
  color: #E72410;
}


/* 自定义筛选时间 */
.financingTime {
  width: 444rpx;
  height: 56rpx;
  font-size: 26rpx;
  margin: -20rpx 0 22rpx 24rpx;
  padding: 8rpx 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
  display: flex;
  align-items: center;
  background-color: #F7F7F7;
}
.financingTime .year {
  min-width: auto;
  flex-shrink: 0;
  line-height: 56rpx;
  text-align: left;
}
.financingTime .short-line {
  width: 200rpx;
  height: 100%;
  line-height: 42rpx;
  text-align: center;
}