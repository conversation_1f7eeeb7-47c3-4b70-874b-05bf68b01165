<wxs src="./changeColor.wxs" module="tools" />
<view class="fin-card">
  <!-- 右边  -->
  <view class="fin-card-r">
    <view class="card_h">
      <view class="card_h_l" bindtap="clickTitle" data-item="{{ objData }}">
        <view
          wx:for="{{ objData.parkName }}"
          wx:key="index"
          class="text {{ tools.checkKey(searchKey,item) ? 'activetext':'' }}"
          >{{item}}</view>
          <view wx:if="{{objData.parkLabel}}" class="tag">
            {{objData.parkLabel}}
          </view>

      </view>
    </view>
    <!-- 标签 -->
    <!-- 其它模块 -->
    <view class="card_con">
      <view class="card_con_i">
        <view>所在地区</view>
        <view>{{ objData.areaStr || '--' }}</view>
      </view>
      <view class="card_con_i">
        <view>企业数量</view>
        <view>{{ objData.settledEnterpriseNumber?objData.settledEnterpriseNumber+'家':'--'}}</view>
      </view>
      <view class="card_con_i">
        <view>建筑面积</view>
        <!-- 返回的就是字符串 -->
        <view style="color: #1E75DB;">{{ objData.parkArea?objData.parkArea+'㎡' : '--' }}</view>
      </view>
    </view>
    <!-- 主导产业 -->
    <view class="industry">
      <text class="industry-label">主导产业：</text>
      <text wx:if="{{objData.leadingIndustry.length==0}}" class="industry-item">--</text>
      <text wx:for="{{objData.leadingIndustry}}" wx:key="index" class="industry-item">{{filterItem[item.leadingIndustryCode]+'　'}}</text>
    </view>
  </view>
</view>
