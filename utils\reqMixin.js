const app = getApp()
module.exports = function (obj) {
  let {
    pname,
    requestFunc
  } = obj
  // 外面需要重新onfresher
  //  behaviors: [ceshiBeh({pname:'',requestFunc:''})], 
  return Behavior({
    data: {
      fresher: true,
      [pname + 'IsFlag']: true, //节流
      [pname + 'HasData']: true, //  是否还有数据
      [pname + 'List']: [], // 列表
      [pname + 'IsNull']: false,
      [pname + 'Parms']: {
        $offset: 0, //偏移量
        $limit: 10, //每页多少条
        $filter: '',
        $count: true,
      }, // 查询参数
    },
    methods: {
      ['reset' + pname + 'Parms'](isFirst) {
        let params = this.data[`${pname}Parms`]
        this.setData({
          [pname + 'IsFlag']: true, //节流
          [pname + 'HasData']: true, //  是否还有数据
          [pname + 'List']: isFirst ? this.data[`${pname}List`] : [], // 列表
          [pname + 'IsNull']: false,
          [pname + 'Parms']: {
            $offset: 0, //偏移量
            $limit: 10, //每页多少条
            $filter: params['$filter'],
            $count: true,
          },
        })
      },
      ['get' + pname + 'List'](callback, isFirst) {
        const that = this;
        let params = that.data[`${pname}Parms`];
        let lists = that.data[`${pname}List`];
        let hasData = that.data[`${pname}HasData`];
        that.setData({
          [pname + 'IsNull']: false,
          [pname + 'IsFlag']: false
        });
        requestFunc(params).then(res => {
          let {
            items: datalist,
            count
          } = res;
          if (datalist.length < params.$limit) hasData = false;
          this.setData({
            [pname + 'IsFlag']: true,
            [pname + 'HasData']: hasData,
            [pname + 'List']: isFirst ? [].concat(datalist) : lists.concat(datalist),
            [pname + 'Count']: count ? count : 0
          }, () => {
            if (!that.data[`${pname}List`]?.length) {
              that.setData({
                [pname + 'IsNull']: true
              });
              callback && callback()
              return
            }
            // 目前没用到-预留
            callback && callback()
          })
        })
      },
      loadMoress(fields) {
        let field = fields && (typeof fields == 'string') ? fields : pname; //同一页面多个下拉 要区分
        const {
          $offset,
          $limit
        } = this.data[field + 'Parms'];
        const hasData = this.data[field + 'HasData'];
        const isFlag = this.data[field + 'IsFlag'];
        if (!hasData || !isFlag) return; //节流和没有数据
        this.setData({
          [field + 'Parms.$offset']: $offset + 10
        }, () => {
          let str = 'get' + field + 'List'
          this[str]()
          // console.log(field, this[str])
        });
      },
      async onfresher(fields) {
        let field = fields && (typeof fields == 'string') ? fields : pname; //同一页面多个下拉 要区分
        this['reset' + field + 'Parms'](true)
        console.log(`外面根据情况重写下拉刷新调用${field}`)
        try {
          let res = await Promise.all([
            this['get' + field + 'List'](() => {}, true)
          ])
          this.setData({
            fresher: false
          }, () => {
            // this.getHeight()
          })
        } catch (err) {
          console.log(err, '错误提示')
          const url = '/pages/errPage/errPage';
          app.route(this, url)
          return
        }

      }
    }
  })
}


// loadMores() {
//   const {
//     activeIndex
//   } = this.data;
//   switch (activeIndex + '') {
//     case '0':
//       // 推荐企业加载更多
//       this.loadMore('recommend');
//       break;
//     case '1':
//       this.loadMore('policy');
//       break;
//     default:
//       break;
//   }
// }
{
  /* <scroll-view class="home" style="height: {{contentH}}px" scroll-y bindscroll="onScroll" throttle="{{true}}" bindscrolltolower="loadMores" scroll-top="{{scrollTop}}" refresher-triggered="{{fresher}}" refresher-background="#0a7de6" refresher-default-style="white" refresher-threshold="{{25}}" bindrefresherrefresh="onfresher" refresher-enabled will-change='transform'>
    <scroll-view> */
} {
  /* <view class="optimization " wx:if="{{activeIndex == 0}}" style="min-height:calc(100vh - {{popTopH}}px);">
  <view wx:if="{{!recommendIsNull}}">
    <informationList entList="{{recommendList}}"></informationList>
    <view wx:if="{{recommendList.length>=recommendParms.$limit}}" style="width: 100%;">
      <template is='more' data="{{hasData:recommendHasData}}"></template>
    </view>
  </view>
  <view style="width: 100%;height:calc(100vh - {{popTopH}}px);" wx:if="{{recommendIsNull}}">
    <template is='null'></template>
  </view>
  </view> */
}