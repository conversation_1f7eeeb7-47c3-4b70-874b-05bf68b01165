/* 弹窗轮播图 */
.swiperBox {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 100;
}

.swiper {
  position: absolute;
  bottom: 20%;
  left: 50%;
  transform: translateX(-50%);
  /* 加了4rpx */
  width: 622rpx;
  height: 832rpx;
}

.swiper>.swiper-c {
  position: relative;
  width: 100%;
  height: 100%;
  /* border: 1px solid blue; */
}

.swiper-item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.fadeIn {
  -webkit-animation: c 0.3s forwards;
  animation: c 0.3s forwards;
}


.swiper .img {
  width: 100%;
  height: 100%;
  /* border: 1px solid red; */
  margin: 2rpx;
  box-sizing: border-box;
  border-radius: 13rpx;
  overflow: hidden;
}

.swiper .close {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 48rpx;
  height: 48rpx;
}

.indicator {
  position: absolute;
  bottom: -40rpx;
  left: 50%;
  transform: translateX(-50%);
  width: auto;
  height: 16rpx;
  display: flex;
}

.indicator>view {
  width: 16rpx;
  height: 16rpx;
  background: #BFBFBF;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 16rpx;
}

.indicator .active {
  background: #FFFFFF !important;
}

@-webkit-keyframes c {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes c {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}