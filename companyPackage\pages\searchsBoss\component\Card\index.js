const app = getApp()
Component({
  properties: {
    obj: {
      type: Object,
      observer(val) {
        this.setData({
          objData: val
        })
      }
    }
  },
  data: {
    objData: {}
  },
  methods: {
    errorFunction(e) {
      this.setData({
        'objData.pic': "https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/image-yonghu.png",
      })
    },
    errorFunctions(e) {
      let {
        index
      } = e.currentTarget.dataset
      let str = `objData.coop_partner[${index}].person_pic`
      this.setData({
        [str]: "https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/image-yonghu.png",
      })
    },
    // 去详情页面 
    goDetail(e) {
      let {
        item,
        type
      } = e.currentTarget.dataset, person_id = "";
      if (type) {
        person_id = item.person_id
      } else {
        person_id = this.data.objData.person_id
      }
      if (!person_id || person_id == 'e0') return;
      // console.log(person_id)
      this.triggerEvent('handleTit', type ? item : this.data.objData)
      const url = encodeURIComponent(`https://reporth5.handidit.com/bossEnter?personid=${person_id}&isShowtupu=true&type=ChanYeTong`)
      // const url = encodeURIComponent(`http://192.168.110.179:8080/bossEnter?personid=${person_id}&isShowtupu=true&type=ChanYeTong`)
      // const url = encodeURIComponent(`http://j-h5-report.ihdwork.com/bossEnter?personid=${person_id}&isShowtupu=true&type=ChanYeTong`)
      app.route(this, `/subPackage/pages/webs/index?url=${url}`)
    },
    goPartner() {
      let person_id = this.data.objData.person_id
      this.triggerEvent('handleTit', this.data.objData)
      const url = encodeURIComponent(`https://reporth5.handidit.com/bPartner?personid=${person_id}&isShowtupu=true&type=ChanYeTong`)
      // const url = encodeURIComponent(`http://192.168.110.179:8080/bossEnter?personid=${person_id}&isShowtupu=true&type=ChanYeTong`)
      // const url = encodeURIComponent(`http://j-h5-report.ihdwork.com/bPartner?personid=${person_id}&isShowtupu=true&type=ChanYeTong`)
      app.route(this, `/subPackage/pages/webs/index?url=${url}`)
    }
  }
})