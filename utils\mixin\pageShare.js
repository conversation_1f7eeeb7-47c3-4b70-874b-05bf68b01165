import {
  mine
} from '../../service/api'
const getShareUrl = function (url) {
  const generatePageLink = wx.getStorageSync('generatePageLink') || false;
  const separator = url.includes('?') ? '&' : '?';
  const paths = generatePageLink ? `${url}${separator}generatePageLink=${generatePageLink}` : url;
  return paths;
}
const handleShareUrl = function () { //分享跳的那个页面需要掉这个
  const isLogin = wx.getStorageSync('TOKEN');
  const myLink = wx.getStorageSync('generatePageLink');
  const launchOptions = wx.getLaunchOptionsSync();
  const generatePageLink = launchOptions.query.generatePageLink;
  // console.log('接收的generatePageLink', generatePageLink);
  if (!generatePageLink || myLink == generatePageLink) return;
  if (!isLogin) {
    wx.setStorageSync('receiveShareLinks', generatePageLink)
    return
  }
  mine.acceptPageLink(generatePageLink)
  // .then(res => {
  //   console.log('请求', res);
  // });
}
const acceptPageLink = function () {
  const myLink = wx.getStorageSync('receiveShareLinks') || false;
  if (!myLink) return;
  mine.acceptPageLink(myLink)
    .then(res => {
      wx.removeStorageSync('receiveShareLinks')
    });
}

module.exports = {
  getShareUrl,
  handleShareUrl,
  acceptPageLink
}