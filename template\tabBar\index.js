import {
  getHeight
} from '../../utils/height';
import {
  hijack
} from '../../utils/route';
const app = getApp()
module.exports = Behavior({
  // behaviors: [],
  properties: {

  },
  data: {
    activeIndex: '0',
    sliderOffset: 0,
    sliderLeft: 0,
    isFixed: false
  },
  methods: {
    handleWidth() {
      //空间被创建时触发
      var that = this;
      var sliderWidth;
      let prpsliderWidth = parseInt(that.properties.sliderWidths) || 0;
      that.getTabWidth(0);
      wx.getSystemInfo({
        success: function (res) {
          if (that.data.tabs.length == 0) return;
          sliderWidth = (res.windowWidth + prpsliderWidth) / that.data.tabs.length;
          that.setData({
            sliderLeft: (res.windowWidth / that.data.tabs.length - sliderWidth) / 2,
            sliderOffset: (res.windowWidth - prpsliderWidth) / that.data.tabs.length * that.data.activeIndex
          });
        }
      });
    },
    setActiveIndex(detail) {
      const {
        tabs
      } = this.data;
      let activeIndex = null;
      if (typeof detail === 'string') {
        for (let i = 0; i < tabs.length; i++) {
          if (tabs[i].title === detail) {
            activeIndex = i;
            break;
          }
        }
      }
      return activeIndex;
    },
    // 获取点击tab宽度,动态设置活动条宽度和偏移位置 
    getTabWidth(id) {
      const query = this.createSelectorQuery();
      const tabClass = '.weui-navbar__item' + id;
      const {
        isFixed
      } = this.data;
      query.selectAll('.weui-navbar').scrollOffset();
      query.select(tabClass).boundingClientRect();
      query.exec(res => {
        // console.log(res);
        const width = res[1]?.width;
        const left = res[1]?.left + res[0][Number(isFixed)]?.scrollLeft;
        this.setData({
          tabScrollLeft: res[0][Number(isFixed)]?.scrollLeft,
          sliderWidth: width,
          sliderLeft: (left) / 2,
          sliderOffset: (left) / 2,
        });
      })
    },
    tabClick(e) { //--外面触发tabClick要触发一次getTabWidth 并设置activeIndex
      console.log('外面需要重写这个方法并赋值给activeIndex:', e.currentTarget)
    }
  },
  lifetimes: {
    attached() {
      this.handleWidth()
      this.getTabWidth(0);
    }
  }
})