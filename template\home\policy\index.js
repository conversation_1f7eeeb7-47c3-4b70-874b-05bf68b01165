import {
  home
} from '../../../service/api';
import {
  hijack
} from '../../../utils/route';
import {
  zcTime
} from '../../../utils/formate'
import {
  formatterParams
} from '../../../utils/util';
const app = getApp()
module.exports = Behavior({
  data: {
    policyOne: false, //1
    policyTwo: false, // 2
    policyThree: false, // 3
    policyFour: false, //4
    // 判断是否选项框有值--高亮---这三个值外面也要用到
    policyOne_val: false,
    policyTwo_val: false,
    policyThree_val: false,
    policyFour_val: false,
    // 
    policyShownavidx: -1,
    policyDropDownMenuTitle: ['范围', '机关', '类型', '时间'],
    order_Data: {
      code: 'issuing_time',
      curVal: '',
      children: JSON.parse(JSON.stringify(zcTime))
    },
    policyThreeData: {
      code: 'type_id',
      curVal: '',
      children: []
    },
    policyTwoData: {
      code: 'issuing_authority_id',
      curVal: '',
      children: []
    },
    // regionData: [], //选中的地区 
    policyOldRegionData: '',
    policyOld: ''
  },
  observers: {
    // regionData: function (list) {
    //   if (list.length > 0) {
    //     this.setData({
    //       policyOne_val: true
    //     })
    //   } else {
    //     this.setData({
    //       policyOne_val: false
    //     })
    //   }
    // },
  },
  methods: {
    // 获取数据 
    async policyPopData() {
      // res1 政策类型弹窗  ----res2 发文机关弹窗 
      const [res1, res2] = await Promise.all([home.getPolicyType(), home.getIssuingAuthority()])
      if (res1?.items.length) {
        let arr = res1.items.map(i => {
          return {
            code: i.id,
            name: i.name
          }
        })
        this.setData({
          'policyThreeData.children': arr
        })
      }
      if (res2?.items.length) {
        let arr = res2.items.map(i => {
          return {
            code: i.id,
            name: i.name
          }
        })
        this.setData({
          'policyTwoData.children': arr
        })
      }
    },
    policyTimeChoose(e) {
      let {
        item,
        source,
        shows
      } = e.currentTarget.dataset
      let soucreData = this.data[source],
        show_val;
      soucreData.children.forEach(i => {
        i.show = false
        if (i.code === item.code) {
          i.show = !item.show
        }
      })
      if (item.show) {
        soucreData.curVal = ''
        show_val = false
      } else {
        soucreData.curVal = item.code
        show_val = true
      }
      this.setData({
        [source]: soucreData,
        [shows]: show_val
      })
      this.policyCloseHyFilter()
      this.policyBackAll()
    },
    // 地区 
    // getPolicRegion(e) {
    //   let data = JSON.parse(JSON.stringify(e.detail)); //解決checked狀態丟失的情況--拷貝--切記
    //   // console.log('获取的地区数据', data)
    //   let {
    //     regionData,
    //     policyOne_val
    //   } = this.data
    //   if (data[0]?.code == 'all') { //说明是全国的 就清除选中的
    //     regionData = []
    //   } else {
    //     regionData = data
    //   }
    //   policyOne_val = regionData.length > 0
    //   this.setData({
    //     regionData,
    //     policyOne_val
    //   });
    //   this.policyCloseHyFilter()
    //   this.policyBackAll()
    // },
    getPolicRegion(e) {
      if (!e.detail.length) return
      let item = e.detail[e.detail.length - 1]
      let {
        code,
        name
      } = item;
      if (code == 'ALL') {
        this.setData({
          policyOldRegionData: '1000000',
          policyOld: code,
          policyOne_val: true
        }, () => {
          this.policyCloseHyFilter()
          this.policyBackAll()
        })
        return
      }
      this.setData({
        policyOldRegionData: code,
        policyOld: code,
        policyOne_val: true
      }, () => {
        this.policyBackAll()
      })
    },


    policyBackAll() {
      // 处理数据 发请求 scope_code 
      const {
        order_Data, //时间
        policyThreeData,
        policyTwoData,
        policyOldRegionData
      } = this.data
      let str = '',
        obj = {};
      if (policyThreeData.curVal) {
        obj[policyThreeData.code] = policyThreeData.curVal
      } else {
        delete obj[policyThreeData.code]
      }
      if (policyTwoData.curVal) {
        obj[policyTwoData.code] = policyTwoData.curVal
      } else {
        delete obj[policyTwoData.code]
      }
      if (!order_Data.curVal) {
        delete obj[order_Data.code]
      } else {
        let arr = order_Data.curVal.split('and')
        obj[order_Data.code] = arr
      }
      // 地区后面来补充
      if (policyOldRegionData.length) {
        obj['scope_code'] = policyOldRegionData
      } else {
        delete obj['scope_code']
      }
      str = formatterParams(obj, {
        scope_code: 'like'
      }, true)
      this.onFlitter('policy', str)
    },



    /**关闭筛选*/
    policyCloseHyFilter: function (e) {
      if (e && e.target.dataset['type'] && e.target.dataset['type'] == 'child') return;
      if (this.data.policyOne) {
        this.setData({
          policyOne: false,
          policyTwo: false,
          policyThree: false,
          policyFour: false,
          policyShownavidx: -1
        })
      } else if (this.data.policyTwo) {
        this.setData({
          policyTwo: false,
          policyOne: false,
          policyThree: false,
          policyFour: false,
          policyShownavidx: -1
        })
      } else if (this.data.policyThree) {
        this.setData({
          policyTwo: false,
          policyOne: false,
          policyThree: false,
          policyFour: false,
          policyShownavidx: -1
        })
      } else if (this.data.policyFour) {
        this.setData({
          policyTwo: false,
          policyOne: false,
          policyThree: false,
          policyFour: false,
          policyShownavidx: -1
        })
      }
    },
    policyRapDistrictNav: hijack(function (e) {
      if (this.data.policyShownavidx != -1) {
        this.policyClickNav(this.data.policyShownavidx)
        this.setData({
          policyShownavidx: -1
        })
        this.policyCloseHyFilter()
        return
      }
      if (this.data.policyOne) {
        this.setData({
          policyOne: false,
          policyTwo: false,
          policyThree: false,
          policyFour: false,
          policyShownavidx: 0
        })
      } else {
        this.setData({
          policyOne: true,
          policyTwo: false,
          policyThree: false,
          policyFour: false,
          policyShownavidx: e.currentTarget.dataset.nav
        })
      }
    }, {
      type: 'searchs-btn',
      app: app
    }),
    policyRapSourceNav: hijack(function (e) {
      if (this.data.policyShownavidx != -1) {
        this.policyClickNav(this.data.policyShownavidx)
        this.setData({
          policyShownavidx: -1
        })
        this.policyCloseHyFilter()
        return
      }
      if (this.data.policyTwo) {
        this.setData({
          policyTwo: false,
          style_open: false,
          policyOne: false,
          policyThree: false,
          policyFour: false,
          policyShownavidx: 0
        })
      } else {
        this.setData({
          policyTwo: true,
          style_open: false,
          policyOne: false,
          policyThree: false,
          policyFour: false,
          policyShownavidx: e.currentTarget.dataset.nav
        })
      }
    }, {
      type: 'searchs-btn',
      app: app
    }),
    policyTapFilterNav: hijack(function (e) {
      if (this.data.policyShownavidx != -1) {
        this.policyClickNav(this.data.policyShownavidx)
        this.setData({
          policyShownavidx: -1
        })
        this.policyCloseHyFilter()
        return
      }
      if (this.data.policyThree) {
        this.setData({
          policyTwo: false,
          policyOne: false,
          policyThree: false,
          policyFour: false,
          policyShownavidx: 0
        })
      } else {
        this.setData({
          policyTwo: false,
          policyOne: false,
          policyThree: true,
          policyFour: false,
          policyShownavidx: e.currentTarget.dataset.nav
        })
      }
    }, {
      type: 'searchs-btn',
      app: app
    }),
    policyFourFilterNav: hijack(function (e) {
      if (this.data.policyShownavidx != -1) {
        this.policyClickNav(this.data.policyShownavidx)
        this.setData({
          policyShownavidx: -1
        })
        this.policyCloseHyFilter()
        return
      }
      if (this.data.policyFour) {
        this.setData({
          policyTwo: false,
          policyOne: false,
          policyThree: false,
          policyFour: false,
          policyShownavidx: 0
        })
      } else {
        this.setData({
          policyTwo: false,
          policyOne: false,
          policyThree: false,
          policyFour: true,
          policyShownavidx: e.currentTarget.dataset.nav
        })
      }
    }, {
      type: 'searchs-btn',
      app: app
    }),
    //点击Nav切换发送请求
    policyClickNav(index) {
      // switch (index) {
      //   case '1':
      //     this.opportunitySure()
      //     break;
      //   case '2':

      //     break;
      //   case '3':
      //     this.canyeSure()
      //     break;

      //   default:
      //     break;
      // }
    },
  },
})