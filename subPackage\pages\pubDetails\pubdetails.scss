@import '/template/null-btn/null-btn.scss';
@import '/template/null/null.scss';

.pub-nav {
  height: 96rpx !important;
}

.pubPage {
  background: #fff;
  height: 100vh;
  overflow: hidden;
}

/* 头部 */
.pub-hd {
  position: relative;
  width: 100%;
  padding: 38rpx 32rpx 40rpx;
  background: #fff;
  height: 240rpx;
}

.pub-hd-img {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.pub-hd-t {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pub-hd-t-l {
  display: flex;
  align-items: center;
  flex: 1;
}

.pub-hd-t-l>image {
  width: 100rpx;
  height: 100rpx;
  flex-shrink: 0;
}

.pub-hd-t-l>text {
  font-size: 32rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 600;
  text-align: LEFT;
  color: #20263a;
  padding-left: 24rpx;
  width: 400rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.pub-hd-t-r {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12rpx;
  height: 52rpx;
  background: rgba(7, 110, 228, 0.06);
  border-radius: 100rpx;
  min-width: 156rpx;
  flex-shrink: 0;
}

.pub-hd-t-r view:nth-child(1) {
  width: 32rpx;
  height: 32rpx;
  margin-right: 4rpx;
}

.pub-hd-t-r view:nth-child(2) {
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #E72410;
}

.pub-hd-note {
  position: relative;
  margin-top: 28rpx;
  display: flex;
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798c;
}

.pub-hd-note view {
  margin-right: 40rpx;
}

/* nabbar */
.pub-tabs {
  background: #fff;
}

/* <!-- 跟进记录 --> */
.record-card {
  padding: 20rpx 24rpx;
  background: #f7f7f7;
}

.record-card-i {
  padding: 28rpx 32rpx 32rpx;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0rpx 4rpx 18rpx 0rpx rgba(221, 221, 221, 0.50);
}

.record-card-h {
  display: flex;
  justify-content: space-between;
  margin-bottom: 54rpx;
}

.record-card-l {
  width: 124rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  background: rgba(231, 36, 16, 0.10);
  border-radius: 4rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color:#E72410;
}

.record-card-r {
  display: flex;
  align-items: center;
}

.record-card-r view:nth-child(1) {
  height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #74798c;
  padding-right: 24rpx;
}

.record-card-r view:nth-child(2) {
  height: 40rpx;
  position: relative;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #E72410;
  padding-left: 24rpx;
}

.record-card-r view:nth-child(2)::after {
  position: absolute;
  content: '';
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: 0;
  height: 60%;
  border-right: 1px solid #eee;
}

.record-card .time {
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798c;
}

.record-card .plan {
  font-size: 24rpx;
  line-height: 34rpx;
  padding-top: 16rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798c;
}

.record-card .plan text {
  color: #20263a;
}

/* 跟进记录 --setps 40+34/2 */
.record-steps {
  padding-top: 57rpx;
  padding-bottom: 40rpx;
  width: 100%;
  background: #f7f7f7;
}

.steps-i {
  position: relative;
  margin: 0 32rpx;
  padding: 0 40rpx 12rpx;
  /* 网上说的1px问题这里没有解决 */
  /* border-top: 1px solid #E72410; */
}

.steps-i::after {
  position: absolute;
  content: "";
  height: 200%;
  left: 0;
  top: 0;
  width: 1px;
  background-color: #E72410;
  -webkit-transform: scale(1, 0.5);
  transform: scale(1, 0.5);
  -webkit-transform-origin: top;
  transform-origin: top;
  z-index: 10;
}

.steps-is::after {
  position: absolute;
  content: "";
  left: 0;
  top: 0;
  width: 0px !important;
}

.steps-i::before {
  position: absolute;
  content: "";
  width: 6px;
  height: 6px;
  top: -3px;
  /* 12/2 + 1px->rpx/2 这步不好算 */
  left: -3px;
  border-radius: 50%;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeBAMAAADJHrORAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAhUExURQlw60dwTABr6QBq6QVu5gZt5AZt5QVu5gVu5Adv5gdu5KeXG0kAAAAKdFJOUxgADwdi98tpj4/DIFTXAAAAm0lEQVQY02MQBAEjBQYmZTCLAUQoMIAAE4xvwAABzBC+EAMMKIL5CnA+E4iPkAYpYIDrhpjAIMiADAQZhFD4igwCKHxGBoj2stAUiAEMYNs4V61a1QC2EaLMC8hfgsTPAvKXIRkTBeQvxcNHUq+AZh6Yz4GwD+IeN7h70N2L7h90/2KEB7IGRtTwZMAa3nATmHDEFzA+gVxIfAIAc9IiVUyuA3wAAAAASUVORK5CYII=') no-repeat;
  background-size: 30rpx;
  background-position: center;
  /* border: 1px solid pink; */
}

.text {
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  margin-bottom: 0 !important;
}

.steps-i-trans .context1 {
  color: #20263a;
  padding-top: 16rpx;
}

.steps-i-trans .context2 {
  color: #74798c;
  padding: 0 10rpx;
}

.steps-i-trans .context3 {
  color: #74798c;
  padding-top: 16rpx;
}

.steps-i-trans .context4 {
  font-size: 24rpx;
  color: #20263a;
  padding: 8rpx 0 48rpx;
}

.steps-i-trans .imgs {
  display: flex;
}

.steps-i-trans .imgs image {
  width: 120rpx;
  height: 120rpx;
  margin-right: 10rpx;
  margin-bottom: 48rpx;
}

.steps-i-trans {
  transform: translateY(-18rpx);
}

.steps-i-t {
  /* 一定要给这个设置高度 因为上面移动就是根据这个标题高度来的  */
  width: 366rpx;
  text-align: LEFT;
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  height: 34rpx;
  line-height: 34rpx;
  color: #E72410;
}

/* 新增绑定按钮  */
.pub-add-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 702rpx;
  height: 40px;
  background: linear-gradient(67deg, #4AB8FF 0%, #E72410 100%);
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  margin: 10px auto 0;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
}

.pub-add-btn>text {
  width: 20rpx;
  height: 20rpx;
  margin-right: 10rpx;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUBAMAAAB/pwA+AAAAAXNSR0IArs4c6QAAABhQTFRFAAAA////////////////////////////I36dXwAAAAd0Uk5TAB87T2Nnh6GTBXAAAAAnSURBVAjXY2AAAvNCBhigGtMYCNJLQSRDORwgMwWBwL0IRDLQyA0ALL0Xgh0f2moAAAAASUVORK5CYII=');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

/*  <!--联系人  --> */
.pub-card-box {
  margin: 0 24rpx 0;
}

.con {
  border-top: 1px solid #f7f7f7;
  height: 600rpx;
  overflow: hidden;
}

.zhanwei {
  height: 20rpx;
  width: 100%;
  background: #f7f7f7;
}




/*   <!-- 商机 --> */