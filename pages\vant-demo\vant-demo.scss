/* pages/vant-demo/vant-demo.scss */

// SCSS 变量定义
$primary-color: #1989fa;
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
$gradient-danger: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);

$border-radius: 12rpx;
$border-radius-large: 16rpx;
$spacing-small: 20rpx;
$spacing-medium: 40rpx;

$text-color-primary: #323233;
$text-color-secondary: #646566;
$bg-color: #f7f8fa;

// SCSS Mixin 定义
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin gradient-button($gradient) {
  background: $gradient;
  border: none;
  border-radius: $border-radius !important;
}

.vant-demo {
  padding: $spacing-small;
  background-color: $bg-color;
  min-height: 100vh;
}

.demo-section {
  margin-bottom: $spacing-medium;
}

.demo-title {
  font-size: 36rpx;
  font-weight: bold;
  color: $text-color-primary;
  text-align: center;
  margin-bottom: $spacing-medium;
  padding: $spacing-medium 0;
  background: $gradient-primary;
  color: white;
  border-radius: $border-radius-large;
}

.demo-subtitle {
  font-size: 28rpx;
  font-weight: 600;
  color: $text-color-primary;
  margin-bottom: $spacing-small;
  padding-left: $spacing-small;
  border-left: 6rpx solid $primary-color;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: $spacing-small;
  padding: 0 $spacing-small;
}

.tab-content {
  padding: $spacing-medium $spacing-small;
  text-align: center;
  color: $text-color-secondary;
  background-color: white;
  margin: $spacing-small 0;
  border-radius: $border-radius;
}

.popup-content {
  padding: $spacing-medium;
  text-align: center;
  background-color: white;

  .popup-title {
    font-size: 32rpx;
    font-weight: bold;
    color: $text-color-primary;
    margin-bottom: $spacing-small;
  }

  .popup-text {
    font-size: 28rpx;
    color: $text-color-secondary;
    margin-bottom: $spacing-medium;
    line-height: 1.6;
  }
}

/* 覆盖 Vant 组件样式 - 使用 SCSS 嵌套和变量 */
.van-search {
  background-color: white;
  border-radius: $border-radius;
  overflow: hidden;
}

.van-cell-group {
  border-radius: $border-radius;
  overflow: hidden;
}

.van-dropdown-menu {
  border-radius: $border-radius;
  overflow: hidden;
}

.van-button {
  border-radius: $border-radius !important;

  // 使用 SCSS 嵌套和 mixin
  &--primary {
    @include gradient-button($gradient-primary);
  }

  &--success {
    @include gradient-button($gradient-success);
  }

  &--warning {
    @include gradient-button($gradient-warning);
  }

  &--danger {
    @include gradient-button($gradient-danger);
  }
}
