import {
  home
} from '../../../service/api';
import {
  formatDate,
  getOldTime
} from '../../../utils/formate'

const app = getApp()

Page({
  data: {
    isIphoneX: app.globalData.isIphoneX,
    item: {},
    aboutList: [],
    tab: [{
        name: '产业研报',
        code: 'a'
      },
      {
        name: '产业资本',
        code: 'b'
      },
    ],
    curCode: 'a'
  },
  onLoad: function (options) {
    let id = options?.id || false
    if (!id) return
    this.setData({
      id
    }) //() => this.request()
  },
  onShow() {
    const {
      id
    } = this.data
    if (id) this.request();
  },
  onUnload() {
    // let route = getCurrentPages()
    // if (route.length) {
    //   let routes = route.filter(item => item.route === 'pages/home/<USER>')
    //   routes.length && routes[0].getInit()
    // }
    if (this.data.share) {
      wx.switchTab({
        url: '/pages/home/<USER>',
      })
    }
  },
  request() {
    this.init()
    this.about()
  },
  async about() {
    let {
      id
    } = this.data
    try {
      const arr = await home.hqdpolishinghotNews(id)
      // console.log('about:', arr)
      arr.forEach(item => {
        let {
          research_enum
        } = item;
        item.create_time = formatDate(item.create_time, 'yyyy-MM-dd')
        item.est = research_enum == 'MACRO_STRATEGY' ? 'ptag1' : research_enum == 'INDUSTRIAL_RESEARCH' ? 'ptag2' : research_enum == 'INDUSTRIAL_INVESTMENT_ATTRACTION' ? 'ptag3' : research_enum == 'MACRO_STRATEGY' ? 'ptag4' : 'ptag1';
        if (item.tag.split(',')?.slice(0, 3)?.join().length > 16) {
          item.tag = item.tag.split(',')?.slice(0, 3).map(i => {
            if (i.length > 5) {
              i = i.slice(0, 4) + '...'
            }
            return i
          })
        } else {
          item.tag = item.tag.split(',').slice(0, 3)
        }
      })
      this.setData({
        aboutList: arr
      })
    } catch (err) {
      console.log(err)
    }
  },
  async init(isLoading) {
    let that = this;
    const {
      id
    } = that.data
    try {
      // let obj = await home.introductionDetail(id, isLoading)
      let obj = await home.hqdpolishingDetail(id, isLoading)
      let {
        research_enum
      } = obj;
      // console.log(obj, research_enum)
      obj.est = research_enum == 'MACRO_STRATEGY' ? 'ptag1' : research_enum == 'INDUSTRIAL_RESEARCH' ? 'ptag2' : research_enum == 'INDUSTRIAL_INVESTMENT_ATTRACTION' ? 'ptag3' : research_enum == 'MACRO_STRATEGY' ? 'ptag4' : 'ptag1';
      obj.create_time = formatDate(obj.create_time, 'yyyy-MM-dd')
      obj.tags = obj.tag.split(',').slice(0, 3)
      obj.size = obj.file_size ? (obj.file_size / (1024 * 1024)).toFixed(2) : 0
      this.setData({
        item: obj
      })
    } catch (err) {
      // app.showToast("未知错误，请稍后再试！", "none", 2000)
      // setTimeout(() => {
      //   wx.navigateBack({
      //     delta: 1,
      //   })
      // }, 2000)
      // console.log(err)
    }
  },
  goDetail(e) {
    console.log(e.currentTarget.dataset.item)
    let {
      id
    } = e.currentTarget.dataset.item
    let url = `/subPackage/pages/polishingDetail/polishing?id=${id}`;
    app.route(this, url)
  },
  // 下载并打开excel文件 
  openExcel(e) {
    let {
      item
    } = e.currentTarget.dataset
    let link = item.file;
    if (!link) {
      app.showToast('暂无数据!', 'none', 1000)
      return
    }

    app.showLoading()
    //下载文件
    wx.downloadFile({
      url: encodeURI(link),
      success(res) {
        if (res.statusCode === 200) {
          const filePath = res.tempFilePath
          if (/\.rar$/.test(link)) {
            wx.setClipboardData({
              data: link,
              success(res) {
                console.log('打开文档失败')
                wx.getClipboardData({
                  success(res) {
                    // console.log(res.data) // data
                    app.showToast('复制成功,请打开浏览器查看!', 'none', 1000)
                  }
                })
              }
            })
            return
          }
          wx.openDocument({
            filePath: filePath,
            showMenu: true, //关键点
            success: function (res) {
              console.log('打开文档成功')
            },
            fail: function () {
              wx.hideLoading()
              wx.setClipboardData({
                data: link,
                success(res) {
                  console.log('打开文档失败')
                  wx.getClipboardData({
                    success(res) {
                      // console.log(res.data) // data
                      app.showToast('复制成功,请打开浏览器查看!', 'none', 1000)
                    }
                  })
                }
              })
            }
          })
        }
      },
      fail: () => {
        wx.hideLoading()
        wx.setClipboardData({
          data: link,
          success(res) {
            console.log('打开文档失败')
            wx.getClipboardData({
              success(res) {
                // console.log(res.data) // data
                app.showToast('复制成功,请打开浏览器查看!', 'none', 1000)
              }
            })
          },
        })
      }
    })
  }
})